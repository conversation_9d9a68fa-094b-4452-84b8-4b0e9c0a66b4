<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/black_75_trans"
    app:cardCornerRadius="5dp"
    app:cardElevation="0dp">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageButton
            android:id="@+id/hint_close"
            android:background="@color/transparent"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginTop="5dp"
            android:src="@drawable/ic_close_white"
            android:minWidth="30dp"
            android:layout_marginEnd="5dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:id="@+id/hint_img"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/img_mimei_popup"
            android:maxWidth="60dp"
            app:layout_constraintBottom_toTopOf="@+id/hint_text"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33"/>
        <TextView
            android:textSize="13sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:id="@+id/hint_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="18dp"
            android:minWidth="182dp"
            android:text="@string/message_click_hint"
            android:lineSpacingExtra="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hint_img"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
