<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/anime_item_layout"
    android:padding="5dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anime_item_cover"
        android:layout_width="160.4dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1.65:1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <ImageView
        android:id="@+id/anime_item_tag_new"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"/>
    <TextView
        android:id="@+id/anime_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/AppTheme.ItemTitleTextView2"/>
    <TextView
        android:id="@+id/anime_item_duration"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toStartOf="@+id/anime_item_like"
        app:layout_constraintStart_toStartOf="@+id/anime_item_title"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_title"
        style="@style/AppTheme.ItemRankInfoTextView"/>
    <ImageView
        android:id="@+id/anime_item_like"
        android:layout_width="35dp"
        android:layout_height="0dp"
        android:src="@drawable/selector_btn_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
