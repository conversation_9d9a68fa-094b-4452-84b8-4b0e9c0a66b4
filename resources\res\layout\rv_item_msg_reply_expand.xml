<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.Guideline
        android:orientation="vertical"
        android:id="@+id/guideline_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.22"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="13sp"
        android:textColor="@color/grey_561"
        android:id="@+id/msg_expand_btn"
        android:background="@color/transparent"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:clickable="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="0dp"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        app:backgroundTint="@color/transparent"
        app:icon="@drawable/ic_arrow_down_yellow"
        app:iconGravity="textEnd"
        app:iconPadding="5dp"
        app:iconSize="15sp"
        app:iconTint="@null"
        app:layout_constraintStart_toStartOf="@+id/guideline_start"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:id="@+id/msg_divider"
        android:background="@color/white_726"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/msg_expand_btn"/>
</androidx.constraintlayout.widget.ConstraintLayout>
