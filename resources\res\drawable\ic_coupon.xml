<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="40dp"
    android:width="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
    <path
        android:fillColor="#f8c236"
        android:pathData="M17.338,27.106C18.131,27.106 18.788,27.695 18.9,28.471C18.912,28.596 19.011,28.696 19.135,28.696H35.241C35.749,28.696 36.146,28.283 36.146,27.782V12.914C36.146,12.401 35.737,12 35.241,12H19.135C19.011,12 18.912,12.1 18.9,12.225C18.788,13.001 18.131,13.59 17.338,13.59"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M15.338,25.106C16.131,25.106 16.788,25.695 16.9,26.471C16.912,26.596 17.011,26.696 17.135,26.696H33.241C33.749,26.696 34.146,26.283 34.146,25.782V10.914C34.146,10.401 33.737,10 33.241,10H17.135C17.011,10 16.912,10.1 16.9,10.225C16.788,11.001 16.131,11.59 15.338,11.59"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"/>
    <path
        android:fillColor="#684012"
        android:pathData="M27.674,21.702C27.934,21.702 28.145,21.489 28.145,21.226C28.145,20.964 27.934,20.751 27.674,20.751C27.414,20.751 27.203,20.964 27.203,21.226C27.203,21.489 27.414,21.702 27.674,21.702Z"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"/>
    <path
        android:fillColor="#684012"
        android:pathData="M21.983,15.945C22.243,15.945 22.454,15.732 22.454,15.469C22.454,15.207 22.243,14.994 21.983,14.994C21.723,14.994 21.512,15.207 21.512,15.469C21.512,15.732 21.723,15.945 21.983,15.945Z"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M28.108,15.069L21.586,21.652"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M13.878,13.94C13.097,14.09 12.341,13.64 12.093,12.901C12.056,12.776 11.944,12.701 11.808,12.726L6.737,13.715C6.241,13.815 5.919,14.29 6.018,14.791L8.82,29.372C8.919,29.872 9.39,30.198 9.886,30.098L14.957,29.109C15.081,29.084 15.156,28.971 15.156,28.834C15.118,28.058 15.651,27.344 16.433,27.194"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M15.338,11.59V12.841"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M15.338,23.855V25.106"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M15.338,14.981V17.272"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M15.338,19.424V21.702"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M12.027,12.09L12.25,13.317"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M12.635,15.419L13.056,17.672"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M11.878,11.94C11.097,12.09 10.341,11.64 10.093,10.901C10.056,10.776 9.944,10.701 9.808,10.726L4.737,11.715C4.241,11.815 3.919,12.29 4.018,12.791L6.82,27.372C6.919,27.872 7.39,28.198 7.886,28.098L12.957,27.109C13.081,27.084 13.156,26.971 13.156,26.834C13.118,26.058 13.651,25.344 14.432,25.194"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
</vector>
