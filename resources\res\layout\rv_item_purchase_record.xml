<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:id="@+id/top_divider"
        android:background="@color/white_726"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="17sp"
        android:textStyle="bold"
        android:textColor="@color/yellow_496"
        android:gravity="center_vertical"
        android:id="@+id/tv_purchase_plan"
        android:paddingTop="14.7dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_divider"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/grey_459"
        android:gravity="center_vertical"
        android:id="@+id/tv_plan_duration_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/purchase_method_days"
        android:paddingStart="15dp"
        android:paddingEnd="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_plan_duration"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_plan_duration"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:id="@+id/tv_plan_duration"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:paddingEnd="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_plan_duration_hint"
        app:layout_constraintTop_toBottomOf="@+id/tv_purchase_plan"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/grey_459"
        android:id="@+id/tv_payment_method_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/purchase_pay_method"
        android:paddingStart="15dp"
        android:paddingEnd="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_payment_method"
        app:layout_constraintStart_toStartOf="@+id/tv_purchase_plan"
        app:layout_constraintTop_toTopOf="@+id/tv_payment_method"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:id="@+id/tv_payment_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_payment_method_price"
        app:layout_constraintStart_toEndOf="@+id/tv_payment_method_hint"
        app:layout_constraintTop_toBottomOf="@+id/tv_plan_duration"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/pink_473"
        android:id="@+id/tv_payment_method_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingEnd="15dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_payment_method"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_payment_method"
        app:layout_constraintTop_toTopOf="@+id/tv_payment_method"/>
</androidx.constraintlayout.widget.ConstraintLayout>
