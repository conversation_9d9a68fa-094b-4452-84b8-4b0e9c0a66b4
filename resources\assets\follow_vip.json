{"nm": "Comp 1", "ddd": 0, "h": 90, "w": 360, "meta": {"g": "@lottiefiles/toolkit-js 0.66.1"}, "layers": [{"ty": 4, "nm": "Shape Layer 1", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "908", "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Rectangle 1", "it": [{"ty": "rc", "nm": "Rectangle Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 15}, "s": {"a": 0, "k": [359.525, 89.926]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.898, 0.4784]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [-0.237, -0.037]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 1}, {"ty": 0, "nm": "tag_btn", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "502", "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [180, 45]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 360, "h": 90, "refId": "1", "ind": 2, "tp": 1}], "v": "5.7.0", "fr": 60, "op": 166, "ip": 0, "assets": [{"nm": "tag_btn", "id": "1", "layers": [{"ty": 0, "nm": "mi<PERSON>_doll", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "907", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128]}, "s": {"a": 0, "k": [43, 43, 100]}, "p": {"a": 0, "k": [64, 41.5, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 256, "h": 256, "refId": "2", "ind": 1}, {"ty": 4, "nm": "Words_01 Outlines", "sr": 1, "st": 0, "op": 600.601, "ip": 0, "ln": "767", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [180, 45, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [180, 58, 0], "t": 10}, {"s": [180, 45, 0], "t": 32}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 16}, {"s": [100], "t": 32}]}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.09, -2.161], [4.475, -2.161], [4.475, -3.331], [1.09, -3.331]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.475, 0.63], [4.475, -0.541], [1.09, -0.541], [1.09, 0.035], [1.072, 0.63]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.649, -0.433], [-0.217, 0.576]], "o": [[0.522, 0.631], [0.522, -0.486], [0, 0]], "v": [[-4.329, 2.376], [-2.583, 3.961], [-1.521, 2.376]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.492, 0.63], [-1.197, 0.63], [-1.18, 0.017], [-1.18, -0.541], [-4.492, -0.541]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.492, -2.161], [-1.178, -2.161], [-1.178, -3.331], [-4.492, -3.331]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.683, -0.756], [-3.674, 0.019], [0.125, -0.72], [2.612, 1.153], [2.089, -0.415], [0.469, 0.504], [-1.009, 0.45], [0.541, 0.81], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.235, 0.919], [2.341, 0.757], [-0.396, 0.487], [-3.944, -0.073], [-1.188, 0.703], [-0.252, -0.487], [1.73, -0.271], [-0.686, -0.558], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.18, -5.078], [-1.18, -6.122], [-8.111, -6.122], [-8.111, -8.049], [7.751, -8.049], [7.751, -6.122], [1.091, -6.122], [1.091, -5.078], [6.617, -5.078], [6.617, 2.376], [0.82, 2.376], [-0.512, 4.915], [8.49, 5.725], [7.572, 7.851], [-2.296, 6.355], [-7.174, 8.049], [-8.49, 6.14], [-4.419, 5.077], [-6.275, 3.043], [-4.617, 2.376], [-6.544, 2.376], [-6.544, -5.078]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [219.035, 56.963]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 2", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.932, -4.862], [3.303, -4.862], [3.303, 4.609], [5.932, 4.609]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[5.932, 4.608]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.112, -6.879], [8.112, 7.687], [5.932, 7.687], [5.932, 6.644], [3.303, 6.644], [3.303, 7.958], [1.215, 7.958], [1.215, -6.879]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.181, -0.613], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.486, -0.594], [0, 0], [0.883, 1.099], [1.749, -1.279], [0.449, 0.324], [-0.305, 2.107], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.451, -0.666], [0.487, 0.269], [-0.36, 2.052]], "o": [[-0.126, 0.63], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.918, 0.918], [0, 0], [-0.576, -0.937], [-0.631, 1.765], [-0.287, -0.449], [2.339, -1.692], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.36, 0.919], [-0.395, -0.36], [0.972, -1.297], [0, 0]], "v": [[-4.024, -8.013], [-4.475, -6.158], [0.298, -6.158], [0.298, -4.16], [-2.242, -4.16], [-2.242, -1.73], [-2.242, -1.297], [0.73, -1.297], [0.73, 0.737], [-2.385, 0.737], [-2.511, 1.476], [0.892, 5.257], [-0.567, 7.094], [-3.07, 3.69], [-6.492, 8.426], [-8.038, 6.787], [-4.564, 0.737], [-7.77, 0.737], [-7.77, -1.297], [-4.419, -1.297], [-4.419, -1.747], [-4.419, -4.16], [-5.141, -4.16], [-6.365, -1.747], [-8.111, -2.989], [-6.094, -8.427]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [254.346, 56.585]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 3", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.402, 1.117], [5.725, 1.117], [5.725, 0.108], [3.402, 0.108]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[3.402, 0.107]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.402, -1.403], [5.725, -1.403], [5.725, -2.376], [3.402, -2.376]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[3.402, -2.376]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.647, 1.117], [1.568, 1.117], [1.568, 0.108], [-0.647, 0.108]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.566, -2.376], [-0.648, -2.376], [-0.648, -1.403], [1.566, -1.403]]}}}, {"ty": "sh", "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[1.566, -1.403]]}}}, {"ty": "sh", "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [1.188, -0.719], [-0.306, -0.18], [0, 0], [0, 0], [0.557, -0.271], [1.08, 0], [0.216, 0.45], [-0.181, 0], [0, 0.199], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.739, 0.287], [0, 0], [-0.739, -0.306], [-0.45, 0.341], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.881, 0.847], [0.396, 0.199], [0, 0], [0, 0], [0, 0.846], [-0.54, 0.27], [-0.053, -0.468], [0.593, 0.037], [0.217, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.81, -0.379], [0, 0], [0.647, 0.235], [0.502, -0.287], [0, 0], [0, 0], [0, 0]], "v": [[5.96, -7.85], [6.374, -7.958], [7.652, -6.932], [4.43, -4.484], [5.51, -3.925], [7.671, -3.925], [7.671, 3.584], [6.951, 5.132], [4.574, 5.402], [4.07, 3.799], [5.456, 3.816], [5.727, 3.564], [5.727, 2.683], [3.402, 2.683], [3.402, 5.329], [1.566, 5.329], [1.566, 2.683], [-0.647, 2.683], [-0.647, 5.384], [-2.557, 5.384], [-2.557, -3.925], [1.531, -3.925], [-0.882, -4.951], [0.505, -6.104], [2.648, -5.276], [4.124, -6.265], [-2.34, -6.265], [-2.34, -7.85]]}}}, {"ty": "sh", "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.522, -0.739], [0, 0], [0.828, 0.901]], "o": [[0.846, 0.865], [0, 0], [-0.487, -0.756], [0, 0]], "v": [[-6.175, -8.463], [-3.781, -5.743], [-5.419, -4.556], [-7.777, -7.472]]}}}, {"ty": "sh", "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.181, 0.377], [-0.341, 0.358], [-0.666, 0.955], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.954, -1.224], [0, 0], [0, 0], [0, 0], [0.45, -1.116], [-2.845, -0.018], [-2.052, 0.199], [0.109, -0.576], [2.106, 0], [1.387, 1.782], [0.881, -0.577], [0.414, 0.289], [-0.595, 2.466], [0, 0], [0.162, -0.161]], "o": [[0.289, -0.073], [0.325, -0.325], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.684, 0.989], [0, 0], [0, 0], [0, 0], [-0.218, 1.493], [1.08, 1.692], [1.908, 0], [-0.306, 0.433], [-1.873, 0.126], [-3.258, 0], [-0.649, 1.044], [-0.27, -0.397], [1.657, -0.989], [0, 0], [-0.434, 0], [-0.108, -0.45]], "v": [[-8.589, 0.325], [-7.652, -0.251], [-5.906, -2.539], [-8.227, -2.539], [-8.227, -4.25], [-4.572, -4.25], [-4.339, -4.34], [-3.024, -3.744], [-5.815, 0.126], [-4.501, 0.126], [-4.158, 0.072], [-3.132, 0.451], [-4.158, 4.375], [1.766, 6.302], [8.697, 5.995], [7.995, 7.994], [1.71, 8.157], [-5.005, 6.032], [-7.309, 8.463], [-8.697, 7.021], [-5.274, 1.801], [-6.787, 1.801], [-7.904, 2.07]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [236.913, 56.549]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 4", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.044, 3.357], [5.142, 3.357], [5.142, 1.431], [-0.044, 1.431]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.044, -3.215], [4.458, -3.215], [4.458, -4.924], [-0.044, -4.924]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.108, 0.522], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.198, -0.594], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.694, -6.672], [1.18, -8.472], [3.755, -8.166], [3.053, -6.672], [6.545, -6.672], [6.545, -1.45], [-0.044, -1.45], [-0.044, -0.333], [7.247, -0.333], [7.247, 5.105], [-2.151, 5.105], [-2.151, -6.672]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.523, -0.721], [0, 0], [0.828, 0.901]], "o": [[0.846, 0.863], [0, 0], [-0.486, -0.774], [0, 0]], "v": [[-6.202, -8.417], [-3.807, -5.716], [-5.446, -4.511], [-7.804, -7.428]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.198, 0.378], [-0.361, 0.342], [-0.684, 0.954], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.99, -1.205], [0, 0], [0, 0], [0, 0], [0.468, -1.135], [-2.845, -0.019], [-2.052, 0.198], [0.109, -0.576], [2.106, 0], [1.385, 1.764], [0.864, -0.577], [0.415, 0.289], [-0.594, 2.466], [0, 0], [0.162, -0.162]], "o": [[0.306, -0.09], [0.342, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.72, 1.008], [0, 0], [0, 0], [0, 0], [-0.233, 1.513], [1.099, 1.656], [1.908, 0], [-0.305, 0.433], [-1.873, 0.126], [-3.24, 0], [-0.631, 1.026], [-0.269, -0.397], [1.639, -0.989], [0, 0], [-0.433, 0], [-0.109, -0.45]], "v": [[-8.615, 0.333], [-7.642, -0.243], [-5.842, -2.53], [-8.254, -2.53], [-8.254, -4.241], [-4.473, -4.241], [-4.24, -4.331], [-2.925, -3.736], [-5.806, 0.135], [-4.492, 0.135], [-4.168, 0.081], [-3.124, 0.459], [-4.168, 4.42], [1.739, 6.311], [8.67, 6.004], [7.968, 8.003], [1.684, 8.166], [-5.013, 6.059], [-7.283, 8.472], [-8.669, 7.03], [-5.266, 1.81], [-6.814, 1.81], [-7.93, 2.079]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [201.136, 56.54]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 5", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.558, -1.63], [4.537, -1.63], [4.537, -3.069], [0.558, -3.069]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[0.558, -3.069]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.537, -6.202], [0.558, -6.202], [0.558, -4.815], [4.537, -4.815]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.325, 0.378], [0, 0.847], [0, 0], [0, 0], [0, 0], [0, 0], [-0.306, -0.611], [-0.522, 0.559], [0, 0], [0.811, -0.504], [-1.207, -0.54], [0.306, -0.593], [0.846, 3.547], [0, 0], [0, 0], [0, 0], [-0.072, -0.485], [0.433, -0.269]], "o": [[0.378, -0.234], [0, 0], [0, 0], [0, 0], [0, 0], [0.198, 0.683], [0.721, -0.558], [0, 0], [-0.792, 0.629], [0.775, 1.061], [-0.469, 0.433], [-2.683, -1.476], [0, 0], [0, 0], [0, 0], [0, 0.612], [-3.53, 0.883], [-0.143, -0.467]], "v": [[-2.395, 6.473], [-1.548, 4.907], [-1.548, -8.057], [6.644, -8.057], [6.644, 0.244], [3.511, 0.244], [4.249, 2.205], [6.284, 0.405], [7.688, 1.936], [5.149, 3.755], [8.085, 6.185], [6.644, 8.057], [1.639, 0.244], [0.558, 0.244], [0.558, 5.32], [3.079, 4.781], [3.169, 6.743], [-1.477, 8.165]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.504, -1.387], [0, -1.062], [0.576, -0.431], [0.396, -0.073], [0.432, 0.018], [0.306, 0.522], [-0.215, 0.018], [-0.126, 0.107], [0, 0.522], [1.027, 1.259], [-0.289, 1.171], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.469, 1.513], [1.08, 1.442], [0, 1.172], [-0.288, 0.216], [-0.325, 0.053], [0, -0.521], [0.324, 0.018], [0.181, 0], [0.216, -0.162], [0, -0.864], [0.342, -1.118], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.799, -8.075], [-3.493, -8.166], [-2.07, -7.355], [-3.637, -2.638], [-2.322, 1.125], [-3.133, 3.52], [-4.159, 3.917], [-5.348, 3.952], [-5.816, 2.098], [-5.024, 2.116], [-4.537, 1.972], [-4.231, 0.891], [-5.474, -2.403], [-4.465, -6.15], [-6.212, -6.15], [-6.212, 8.057], [-8.085, 8.057], [-8.085, -8.075]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [183.577, 56.882]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 6", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.126, -1.008], [0, 0], [0, 0], [0, 0], [0, 0], [-0.776, 0], [0, 0], [-0.108, 2.322], [-0.612, -0.145], [1.981, 0], [0, 0], [0, 2.179], [0, 0], [4.338, -1.782], [0.558, 0.45], [-0.755, 2.738], [0, 0], [0, 0], [0, 0], [-0.037, 1.008], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.054, 1.008], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.702], [0, 0], [0.739, 0], [0.467, 0.378], [-0.252, 3.078], [0, 0], [-2.287, 0], [0, 0], [-0.846, 3.241], [-0.308, -0.558], [3.979, -1.513], [0, 0], [0, 0], [0, 0], [0.127, -1.008], [0, 0], [0, 0], [0, 0]], "v": [[7.301, -7.877], [7.301, -5.771], [0.783, -5.771], [0.549, -2.728], [8.292, -2.728], [8.292, -0.604], [2.35, -0.604], [2.35, 4.547], [3.251, 5.339], [5.447, 5.339], [6.402, 2.656], [8.471, 3.557], [5.626, 7.463], [2.945, 7.463], [0.063, 4.582], [0.063, -0.063], [-7.012, 7.877], [-8.471, 6.077], [-2.044, -0.604], [-8.165, -0.604], [-8.165, -2.728], [-1.648, -2.728], [-1.43, -5.771], [-7.085, -5.771], [-7.085, -7.877]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [165.1, 57.098]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 7", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.113, -1.256], [-2.594, -1.256], [-2.594, 0.392], [4.113, 0.392]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.032, -0.416], [0, 0], [0, 0]], "o": [[0, 0], [0, 0.368], [0, 0], [0, 0], [0, 0]], "v": [[-2.594, 1.417], [-2.594, 1.913], [-2.642, 3.097], [4.113, 3.097], [4.113, 1.417]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.24, -0.592], [0, 0], [0, 0], [0, 0], [0.369, -0.592], [0, 0], [0, 0], [0.528, -0.208], [1.377, 0], [0.16, 0.336], [-0.304, 0.017], [0, 0.241], [0, 0], [0, 0], [0.961, -0.88], [0.225, 0.176], [0, 1.505], [0, 0], [0.976, -0.689], [0.239, 0.239], [-1.008, 2.065], [0, 0], [0, 0], [0, 0], [-0.191, 0.689]], "o": [[-0.192, 0.592], [0, 0], [0, 0], [0, 0], [-0.305, 0.641], [0, 0], [0, 0], [0, 0.704], [-0.528, 0.176], [-0.064, -0.32], [1.056, 0.048], [0.272, 0], [0, 0], [0, 0], [-0.224, 1.184], [-0.144, -0.256], [1.329, -1.281], [0, 0], [-0.752, 0.929], [-0.176, -0.272], [1.872, -1.281], [0, 0], [0, 0], [0, 0], [0.272, -0.688], [0, 0]], "v": [[-0.385, -7.115], [-1.025, -5.322], [7.17, -5.322], [7.17, -4.202], [-1.505, -4.202], [-2.498, -2.345], [5.297, -2.345], [5.297, 5.819], [4.609, 7.099], [1.824, 7.275], [1.424, 6.123], [3.761, 6.138], [4.113, 5.802], [4.113, 4.138], [-2.786, 4.138], [-4.435, 7.403], [-5.267, 6.475], [-3.794, 1.897], [-3.794, -0.472], [-6.387, 1.993], [-7.171, 1.065], [-2.818, -4.202], [-6.852, -4.202], [-6.852, -5.322], [-2.306, -5.322], [-1.602, -7.403]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [147.966, 57.348]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 8", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.777, -1.512], [3.778, -1.512], [3.778, -2.952], [-3.777, -2.952]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.043, -0.616], [-4.978, -0.616], [-4.978, -3.849], [5.043, -3.849]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[5.043, -3.849]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.175, -0.481], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.192, 0.383]], "o": [[0.272, 0.448], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.16, -0.433], [0, 0]], "v": [[0.096, -7.435], [0.8, -5.93], [6.947, -5.93], [6.947, -4.89], [-6.994, -4.89], [-6.994, -5.93], [-0.544, -5.93], [-1.104, -7.21]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.777, -0.448], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.577, -0.161], [1.441, 0], [0.176, 0.305], [-0.288, 0.016], [0, 0.225], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.008, 0.385], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.361, 0.768], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.673], [-0.56, 0.143], [-0.08, -0.32], [1.104, 0.049], [0.288, -0.017], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.057, -0.24], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.786, 0.296], [5.587, 0.985], [0.592, 2.857], [0.592, 3.257], [7.155, 3.257], [7.155, 4.314], [0.592, 4.314], [0.592, 6.17], [-0.176, 7.292], [-3.089, 7.435], [-3.521, 6.426], [-1.04, 6.443], [-0.672, 6.138], [-0.672, 4.314], [-7.155, 4.314], [-7.155, 3.257], [-0.672, 3.257], [-0.656, 2.313], [2.593, 1.336], [-5.65, 1.336], [-5.65, 0.36], [4.194, 0.36], [4.514, 0.344]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [132.125, 57.268]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 9", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.422, -0.39], [0.762, 0], [0, 0], [0, 0], [0, 0], [-0.422, -0.384], [0, -0.726]], "o": [[0, 0.715], [-0.422, 0.389], [0, 0], [0, 0], [0, 0], [0.762, 0], [0.422, 0.384], [0, 0]], "v": [[2.079, -1.973], [1.446, -0.316], [-0.329, 0.268], [-2.122, 0.268], [-2.122, -4.232], [-0.329, -4.232], [1.446, -3.656], [2.079, -1.991]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.37, 0.651], [0.668, 0.361], [0.891, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.668, 0.36], [-0.373, 0.65], [0, 0.867], [0, 0]], "o": [[-0.373, -0.65], [-0.668, -0.36], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.891, 0], [0.668, -0.361], [0.37, -0.651], [0, 0], [0, -0.873]], "v": [[4.22, -4.285], [2.659, -5.801], [0.321, -6.341], [-4.776, -6.341], [-4.776, 6.341], [-2.122, 6.341], [-2.122, 2.351], [0.321, 2.351], [2.659, 1.811], [4.22, 0.295], [4.777, -1.982], [4.777, -1.999]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.5176, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [182.298, 37.61]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 10", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.327, -6.341], [1.327, -6.341], [1.327, 6.341], [-1.327, 6.341]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.5176, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [174.085, 37.611]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 11", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.863, -6.341], [-2.926, -6.341], [-0.027, 3.626], [0.026, 3.626], [2.917, -6.341], [5.863, -6.341], [1.617, 6.341], [-1.626, 6.341]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.5176, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [165.604, 37.61]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 12", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.432, -0.832], [0, 0], [0.816, 0.977]], "o": [[0.833, 0.944], [0, 0], [-0.416, -0.848], [0, 0]], "v": [[1.328, -0.288], [3.569, 2.785], [2.513, 3.361], [0.336, 0.192]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.337, -0.656], [0, 0], [0.64, 0.752]], "o": [[0.656, 0.704], [0, 0], [-0.32, -0.656], [0, 0]], "v": [[-4.034, -6.867], [-2.289, -4.514], [-3.378, -3.986], [-5.058, -6.418]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.032, -0.673], [0, 0], [0, -0.192], [0.624, -0.737], [0.592, -0.064], [0.993, 0.048], [0.24, 0.353], [-0.384, 0], [-0.177, 0.193], [-0.224, 5.442], [0, 0], [4.098, -2.721], [0.288, 0.192], [-0.4, 2.849], [0, 0], [0, 0], [0, 0], [0, 0.608], [0, 0]], "o": [[0, 0], [0, 0.592], [0, 0], [0, 0], [-0.256, 6.419], [-0.368, 0.464], [-0.56, 0.048], [-0.015, -0.368], [1.088, 0.096], [0.352, 0.016], [0.48, -0.496], [0, 0], [-0.385, 3.137], [-0.208, -0.272], [3.841, -2.497], [0, 0], [0, 0], [0, 0], [0.048, -0.689], [0, 0], [0, 0]], "v": [[0.143, -7.283], [0.143, -5.378], [0.096, -3.457], [6.77, -3.457], [6.754, -2.833], [5.65, 6.579], [4.321, 7.235], [1.76, 7.203], [1.344, 5.954], [3.825, 6.05], [4.53, 5.826], [5.49, -2.257], [-0.032, -2.257], [-5.827, 7.187], [-6.771, 6.307], [-1.265, -2.257], [-6.339, -2.257], [-6.339, -3.457], [-1.137, -3.457], [-1.073, -5.394], [-1.073, -7.283]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [147.757, 37.821]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 13", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.624, -0.545], [0, 0], [1.024, 0.529]], "o": [[1.009, 0.496], [0, 0], [-0.608, -0.576], [0, 0]], "v": [[3.497, -7.363], [6.394, -5.538], [5.642, -4.706], [2.777, -6.627]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.048, -0.88], [0, 0], [0, 0], [0, 0], [-0.416, -1.553], [-0.512, 1.616], [0, 0], [1.185, -1.536], [-0.816, 0], [-0.096, 2.129], [-0.32, -0.112], [1.232, 0], [0.736, 1.649], [1.233, -0.736], [0.256, 0.241], [-1.057, 1.217], [0.192, 2.592], [0, 0], [0, 0], [0, 0], [0, -0.16], [0.336, -0.432], [0.432, -0.048], [0.785, 0.048], [0.192, 0.305], [-0.273, 0], [-0.128, 0.144], [-0.064, 2.753], [0, 0], [0, 0], [1.473, -2.113], [0.288, 0.144], [0, 2.081], [0, 0], [0, 0], [0, 0.912]], "o": [[0, 0.928], [0, 0], [0, 0], [0, 0], [0.176, 2.144], [0.864, -1.281], [0, 0], [-0.688, 2.097], [0.593, 1.569], [0.464, 0], [0.273, 0.272], [-0.208, 2.738], [-1.232, 0], [-0.977, 1.056], [-0.192, -0.256], [1.425, -0.752], [-0.609, -1.84], [0, 0], [0, 0], [0, 0], [0, 0], [-0.081, 3.601], [-0.256, 0.304], [-0.416, 0.032], [-0.016, -0.336], [0.736, 0.08], [0.24, 0], [0.208, -0.241], [0, 0], [0, 0], [0, 2.257], [-0.208, -0.24], [1.328, -1.969], [0, 0], [0, 0], [-0.048, -0.88], [0, 0]], "v": [[1.993, -7.411], [2.057, -4.706], [7.259, -4.706], [7.259, -3.537], [2.121, -3.537], [3.017, 2.065], [5.082, -2.289], [6.282, -1.985], [3.465, 3.473], [5.594, 5.954], [6.346, 2.993], [7.387, 3.649], [5.498, 7.187], [2.569, 4.546], [-0.745, 7.219], [-1.625, 6.226], [2.089, 3.265], [0.904, -3.537], [-4.666, -3.537], [-4.666, -1.425], [-0.552, -1.425], [-0.568, -0.912], [-1.065, 4.482], [-2.025, 4.946], [-3.962, 4.93], [-4.298, 3.809], [-2.601, 3.889], [-2.089, 3.714], [-1.737, -0.304], [-4.666, -0.304], [-4.666, -0.192], [-6.379, 7.411], [-7.387, 6.611], [-5.915, -0.208], [-5.915, -4.706], [0.824, -4.706], [0.744, -7.411]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [132.07, 37.933]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2}, {"ty": 4, "nm": "dot Outlines", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "83", "hasMask": true, "ao": 0, "ks": {"a": {"a": 0, "k": [359.5, 0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [359.5, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "masksProperties": [{"nm": "Mask 1", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.207, -14.086], [-9.332, -10.318], [-10.85, -0.068], [8.65, 8.789]], "o": [[0.207, 14.085], [9.332, 10.318], [10.85, 0.068], [-8.65, -8.79]], "v": [[341.543, -31.21], [355.543, 1.932], [390.9, 18.932], [402.4, -40.71]]}], "t": 18}, {"s": [{"c": true, "i": [[-0.207, -14.086], [-9.332, -10.318], [-10.85, -0.068], [8.65, 8.789]], "o": [[0.207, 14.085], [9.332, 10.318], [10.85, 0.068], [-8.65, -8.79]], "v": [[306.793, 4.04], [320.793, 37.182], [356.15, 54.182], [367.65, -5.46]]}], "t": 29}]}}], "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 2", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.63]], "o": [[0.623, 0], [0, -0.63], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.143], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 3", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 4", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 5", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 6", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.143], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 7", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 8", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 9", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.637], [0.628, 0], [0, 0.638]], "o": [[0.618, 0], [0, 0.638], [-0.629, 0], [0, -0.637]], "v": [[0, -1.143], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 32.535]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 10", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 11", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 12", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 53.038]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 13", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 14", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 15", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 16", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 17", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 18", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 19", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 20", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 21", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 22", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 23", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 24", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 25", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 26", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 27", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 28", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 29", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 30", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 31", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 32", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 33", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.637], [0.628, 0], [0, 0.638]], "o": [[0.618, 0], [0, 0.638], [-0.629, 0], [0, -0.637]], "v": [[0, -1.143], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 32.535]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 34", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 35", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 36", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 53.038]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 37", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.101, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.654, 18.857]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 38", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.656, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 39", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.101], [0.101, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.102], [-0.1, 0.624], [0.615, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.648, 32.52]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 40", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.101, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.66, 39.358]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 41", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.614, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.101], [-0.1, 0.624], [0.616, 0.101]], "v": [[1.112, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.182, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.653, 46.194]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 42", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.101, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.645, 53.03]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 43", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 44", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.63]], "o": [[0.623, 0], [0, -0.63], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.143], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 45", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 46", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 47", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 48", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.143], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 49", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 50", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 51", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 52", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 53", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 12.031]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 54", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 55", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 56", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 57", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 58", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 59", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 60", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 61", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 62", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 63", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 64", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 65", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 12.031]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 66", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 67", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.614, 0.102], [0.1, -0.623], [-0.614, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.101], [-0.1, 0.624], [0.615, 0.101]], "v": [[1.113, 0.184], [0.182, -1.129], [-1.112, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.649, 5.183]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 68", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.652, 12.02]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 69", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.654, 18.858]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 70", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 71", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 72", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3}, {"ty": 4, "nm": "Small_circle", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "95", "ddd": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129, -19, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [255.939, -23.516, 0], "t": 0}, {"s": [309, 26, 0], "t": 29}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "or": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [225.91, 229.057, 180], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [37.335, 132.364, 299.63], "t": 14}, {"s": [0, 0, 0], "t": 29}]}}, "shapes": [{"ty": "gr", "nm": "Ellipse 1", "it": [{"ty": "el", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [34.697, 34.697]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.908, -18.987]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50}}]}], "ind": 4}, {"ty": 4, "nm": "Small_circle 2", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "98", "ddd": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129, -19, 0]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [354, 354, 354], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 29}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [243.013, 124.785, 0], "t": 0}, {"s": [315.5, 87.5, 0], "t": 29}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "or": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [225.91, 229.057, 180], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [37.335, 132.364, 299.63], "t": 14}, {"s": [0, 0, 0], "t": 29}]}}, "shapes": [{"ty": "gr", "nm": "Ellipse 1", "it": [{"ty": "el", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [34.697, 34.697]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.5176, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.908, -18.987]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 25}}]}], "ind": 5}, {"ty": 4, "nm": "BG_face", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "100", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Rectangle 1", "it": [{"ty": "rc", "nm": "Rectangle Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [364.008, 93.93]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.9843, 0.9451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0.004, -0.035]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6}]}, {"nm": "mi<PERSON>_doll", "id": "2", "layers": [{"ty": 4, "nm": "Head_obj_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "905", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [75.227, 57.274, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [75.227, 57.274, 0], "t": 45.517}, {"s": [87.227, 57.274, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-1.829, -0.979], [-8.449, 10.198], [-5.436, 1.987], [0.135, 0.136], [12.301, -12.314], [-12.32, -12.307]], "o": [[6.493, -14.202], [4.646, -5.608], [-0.13, -0.136], [-12.32, -12.307], [-12.302, 12.314], [1.85, 1.848]], "v": [[-10.782, 30.553], [13.275, -6.774], [28.634, -17.84], [28.249, -18.246], [-16.332, -18.233], [-16.298, 26.348]]}], "t": 45.517}, {"s": [{"c": true, "i": [[-1.829, -0.979], [-8.449, 10.198], [5.07, 5.626], [0.135, 0.136], [12.301, -12.314], [-12.32, -12.307]], "o": [[13.582, 6.147], [4.646, -5.608], [-0.13, -0.136], [-12.32, -12.307], [-12.302, 12.314], [1.85, 1.848]], "v": [[-10.782, 30.553], [28.632, 4.601], [28.634, -17.84], [28.249, -18.246], [-16.332, -18.233], [-16.298, 26.348]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [68.296, 53.247]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 1, "parent": 8}, {"ty": 4, "nm": "shine line_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "904", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [113.311, 133.985, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113.311, 133.985, 0], "t": 45.517}, {"s": [121.54, 133.985, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.157, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [113.424, 132.662]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2, "parent": 7}, {"ty": 4, "nm": "shine line_M Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "903", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129.088, 133.44, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [129.088, 133.44, 0], "t": 45.517}, {"s": [137.318, 133.44, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.157, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [127.945, 132.654]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3, "parent": 7}, {"ty": 4, "nm": "shine line_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "902", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [142.689, 132.896, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [142.689, 132.896, 0], "t": 45.517}, {"s": [150.919, 132.896, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.156, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [142.463, 132.646]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4, "parent": 7}, {"ty": 4, "nm": "Faceobj_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "901", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [79.58, 125.28, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [136, 100, 100], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [79.58, 125.28, 0], "t": 45.517}, {"s": [88.58, 125.28, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.786, -3.199], [-7.199, 3.204], [7.199, -3.204], [1.786, 3.199]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.5176, 0]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [78.837, 125.299]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 5, "parent": 7}, {"ty": 4, "nm": "Faceobj_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "900", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [177.508, 126.912, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [177.508, 126.912, 0], "t": 45.517}, {"s": [185.508, 126.912, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.786, -3.199], [-7.199, 3.204], [7.199, -3.204], [1.786, 3.199]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.5176, 0]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [176.437, 125.247]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6, "parent": 7}, {"ty": 4, "nm": "Face Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "899", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [81, 100, 100], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 45.517}, {"s": [151.337, 127.912, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.063, 0.039], [9.283, 12.379], [7.002, 6.467], [8.248, 9.509], [8.804, -8.148], [2.311, -3.089], [-15.953, -9.804], [0.312, -0.056], [-16.545, 0.009], [-9.674, 2.309]], "o": [[15.942, -9.821], [-2.314, -3.087], [-8.813, -8.138], [-8.238, 9.518], [-6.995, 6.474], [-9.269, 12.39], [0.055, 0.034], [9.69, 2.314], [16.486, -0.009], [-0.419, -0.065]], "v": [[38.366, 40.198], [42.87, -1.49], [27.779, -16.377], [-0.039, -43.958], [-27.828, -16.347], [-42.904, -1.445], [-38.355, 40.239], [-38.759, 40.381], [0.134, 43.949], [38.921, 40.365]]}], "t": 45.517}, {"s": [{"c": true, "i": [[-0.063, 0.039], [9.283, 12.379], [7.002, 6.467], [8.248, 9.509], [8.804, -8.148], [2.311, -3.089], [-15.953, -9.804], [0.312, -0.056], [-16.545, 0.009], [-9.674, 2.309]], "o": [[15.942, -9.821], [-2.314, -3.087], [-8.813, -8.138], [-8.238, 9.518], [-6.995, 6.474], [-9.269, 12.39], [0.055, 0.034], [9.69, 2.314], [16.486, -0.009], [-0.419, -0.065]], "v": [[38.366, 40.198], [46.509, 4.406], [31.418, -11.071], [-0.039, -43.958], [-27.828, -16.347], [-42.904, -1.445], [-38.355, 40.239], [-38.759, 40.381], [0.443, 42.949], [38.921, 40.365]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [130.41, 115.849]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 7, "parent": 8}, {"ty": 4, "nm": "Hair Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "898", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 113, 0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 127, 0], "t": 18.621}, {"s": [128, 128, 0], "t": 33.103}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[16.581, 19.969], [12.281, -4.617], [14.973, -18.072], [-3.125, -30.025], [-4.204, 0.751], [-16.545, 0.009], [-9.674, 2.309], [-3.211, 31.17]], "o": [[-14.992, -18.056], [-12.286, -4.603], [-16.56, 19.987], [3.282, 31.523], [9.69, 2.315], [16.486, -0.009], [4.842, 0.75], [3.093, -30.029]], "v": [[48.759, -47.665], [-0.032, -56.996], [-48.813, -47.613], [-81.39, 32.861], [-38.733, 62.144], [0.16, 65.712], [38.947, 62.128], [81.422, 32.774]]}], "t": 45.517}, {"s": [{"c": true, "i": [[16.362, 20.149], [12.281, -4.617], [17.635, -21.046], [-3.125, -30.025], [-4.204, 0.751], [-16.545, 0.009], [-11.307, 1.749], [-3.211, 31.17]], "o": [[-11.316, -13.935], [-12.286, -4.603], [-16.671, 19.895], [3.282, 31.523], [11.084, 0.557], [16.486, -0.01], [4.842, 0.75], [3.093, -30.029]], "v": [[51.504, -46.881], [12.908, -56.604], [-48.813, -47.613], [-81.39, 32.861], [-12.069, 62.928], [14.668, 64.144], [41.3, 62.912], [81.422, 32.774]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [130.383, 94.086]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 8, "parent": 10}, {"ty": 4, "nm": "Head_obj_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "897", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [168.259, 71.963, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [93, 93, 98.936], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [168.259, 71.963, 0], "t": 45.517}, {"s": [165.342, 76.036, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-12.314, -12.3], [-12.307, 12.319], [12.314, 12.3], [12.307, -12.321]], "o": [[12.314, 12.301], [12.307, -12.321], [-12.314, -12.301], [-12.307, 12.319]], "v": [[-14.11, 14.132], [22.297, 22.282], [22.284, -22.3], [-22.297, -22.265]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [182.24, 57.233]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 9, "parent": 8}, {"ty": 4, "nm": "Body Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "896", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 360, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 10.345}, {"s": [128, 141, 0], "t": 20.689}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [-19.678, 0], [0, 0], [0, -20.376], [0, 0]], "o": [[0, 0], [0, -20.376], [0, 0], [19.678, 0], [0, 0], [0, 0]], "v": [[-54.824, 55.653], [-54.824, -18.759], [-19.194, -55.654], [19.193, -55.654], [54.824, -18.759], [54.824, 55.653]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.256, 195.683]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 10}, {"ty": 4, "nm": "R_Arm Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "895", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [169.348, 173.7, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [169.348, 173.7, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-30], "t": 20.689}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 31.034}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 45.517}, {"s": [-60], "t": 66.206}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[8.271, -8.271], [8.271, 8.271], [-8.271, 8.271], [-8.271, -8.271]], "o": [[-8.271, 8.271], [-8.271, -8.271], [8.271, -8.271], [8.271, 8.271]], "v": [[28.686, 28.686], [-28.686, 1.267], [-28.686, -28.686], [1.267, -28.686]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [187.971, 192.417]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11, "parent": 10}, {"ty": 4, "nm": "L_Arm Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "894", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [85.02, 173.7, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [85.02, 173.7, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [33], "t": 20.689}, {"s": [0], "t": 31.034}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-8.271, -8.271], [-8.271, 8.271], [8.271, 8.271], [8.271, -8.271]], "o": [[8.271, 8.271], [8.271, -8.271], [-8.271, -8.271], [-8.271, 8.271]], "v": [[-28.686, 28.686], [28.686, 1.267], [28.686, -28.686], [-1.267, -28.686]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [68.539, 192.417]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 12, "parent": 10}]}]}