<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="15dp"
    android:layout_marginBottom="15dp">
    <LinearLayout
        android:layout_gravity="center_horizontal"
        android:orientation="horizontal"
        android:id="@+id/load_more_loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ProgressBar
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            style="?android:attr/progressBarStyleSmall"/>
        <TextView
            android:textSize="16sp"
            android:textColor="@color/colorPrimary"
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/load_more_loading"/>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/load_more_load_fail_view"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="16sp"
            android:textColor="@color/colorPrimary"
            android:layout_gravity="center"
            android:id="@+id/load_fail_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/load_more_failed"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/load_more_load_end_view"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="16sp"
            android:textColor="@color/colorPrimary"
            android:layout_gravity="center"
            android:id="@+id/load_end_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/load_more_end"/>
    </FrameLayout>
</FrameLayout>
