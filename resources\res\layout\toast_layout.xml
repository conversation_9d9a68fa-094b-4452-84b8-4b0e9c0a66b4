<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/toast_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="12sp"
        android:textColor="@color/black_9"
        android:gravity="center"
        android:layout_gravity="center"
        android:id="@+id/toast_message"
        android:background="@drawable/bg_toast"
        android:padding="12dp"
        android:layout_width="260dp"
        android:layout_height="match_parent"/>
</LinearLayout>
