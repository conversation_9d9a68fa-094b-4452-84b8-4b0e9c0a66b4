<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fiction_item_layout"
    android:background="@color/white"
    android:paddingBottom="5dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/fiction_item_tag_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:id="@+id/fiction_item_title"
        android:paddingTop="15sp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintEnd_toStartOf="@+id/type_tag"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_459"
        android:id="@+id/fiction_item_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/fiction_item_title"
        app:layout_constraintStart_toStartOf="@+id/fiction_item_title"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_title"
        style="@style/Theme.Text.ThreeLine"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/yellow_497"
        android:gravity="center"
        android:id="@+id/type_tag"
        android:background="@drawable/bg_rank_type_tag"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="15dp"
        android:text="@string/collect_text_continue_reading"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="3.18:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.2"/>
</androidx.constraintlayout.widget.ConstraintLayout>
