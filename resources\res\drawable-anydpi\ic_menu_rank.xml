<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="50dp"
    android:width="50dp"
    android:viewportWidth="50"
    android:viewportHeight="50">
    <path
        android:fillColor="#f6f1e9"
        android:pathData="M25,25m-25,0a25,25 0,1 1,50 0a25,25 0,1 1,-50 0"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M32.008,11.811V13.18L32.052,15.565V17.155C32.052,18.392 31.657,19.629 30.998,20.645L27.387,25.819C27.307,25.932 27.177,26 27.039,26H23.972C23.831,26 23.699,25.93 23.62,25.812L20.148,20.645C19.445,19.629 19.094,18.392 19.094,17.155V15.565V13.18V11.811C19.094,10.795 19.884,10 20.895,10H30.207C31.218,10 32.008,10.795 32.008,11.811Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M32.008,13.18V11.811C32.008,10.795 31.218,10 30.207,10H20.895C19.884,10 19.094,10.795 19.094,11.811V13.18V16.204V17.155C19.094,18.392 19.445,19.629 20.148,20.645L23.746,26M32.052,17.155C32.052,18.392 31.657,19.629 30.998,20.645L30.006,22.174L29.51,22.939"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#ff8400"
        android:pathData="M11.15,20.798C11.232,20.444 11.666,20.311 11.932,20.559L12.875,21.44L14.137,21.321C14.509,21.286 14.772,21.677 14.6,22.008L14.042,23.08L14.526,24.172C14.677,24.513 14.391,24.886 14.023,24.829L12.792,24.64L11.799,25.471C11.519,25.705 11.091,25.547 11.031,25.187L10.833,24L9.76,23.391C9.434,23.206 9.444,22.733 9.778,22.562L10.875,22L11.15,20.798Z"/>
    <path
        android:fillColor="#ff8400"
        android:pathData="M39.838,20.82C39.763,20.461 39.323,20.321 39.054,20.572L38.125,21.44L36.863,21.321C36.491,21.286 36.228,21.677 36.4,22.008L36.958,23.08L36.454,24.164C36.295,24.505 36.58,24.885 36.951,24.829L38.208,24.64L39.201,25.471C39.481,25.705 39.909,25.547 39.969,25.187L40.167,24L41.231,23.396C41.56,23.21 41.547,22.733 41.21,22.564L40.083,22L39.838,20.82Z"/>
    <path
        android:fillColor="#fffae4"
        android:pathData="M25.927,13.874C25.764,13.582 25.344,13.582 25.182,13.874L24.499,15.101L23.144,15.336C22.806,15.394 22.672,15.806 22.909,16.052L23.858,17.036L23.688,18.424C23.648,18.758 23.991,19.005 24.295,18.861L25.554,18.264L26.841,18.862C27.147,19.005 27.49,18.752 27.444,18.417L27.25,17.036L28.199,16.052C28.437,15.806 28.302,15.394 27.965,15.336L26.61,15.101L25.927,13.874Z"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M36.679,28.777C36.802,28.529 36.7,28.23 36.444,28.124C35.167,27.591 31.442,26.293 25.561,26.293C19.68,26.293 15.955,27.591 14.677,28.124C14.422,28.23 14.32,28.529 14.443,28.777L16.673,33.28C16.793,33.522 17.085,33.629 17.339,33.535C18.388,33.15 21.239,32.278 25.561,32.278C29.66,32.278 32.419,33.046 33.593,33.459C33.914,33.572 34.389,33.4 34.54,33.095L36.679,28.777Z"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M11.317,31.331L13.945,30.204L15.513,29.704L17.513,34.704L17.935,35.856C18.011,36.029 17.932,36.231 17.759,36.307L13.945,37.991C13.718,38.092 13.463,37.925 13.463,37.678V34.305C13.463,34.215 13.427,34.128 13.363,34.064L11.209,31.887C11.039,31.716 11.095,31.427 11.317,31.331Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M13.945,30.204L11.317,31.331C11.095,31.427 11.039,31.716 11.209,31.887L13.363,34.064C13.427,34.128 13.463,34.215 13.463,34.305V37.678C13.463,37.925 13.718,38.092 13.945,37.991L17.759,36.307"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M39.66,31.831L37.033,30.704L35.464,30.204L33.464,35.204L33.042,36.356C32.966,36.529 33.045,36.731 33.218,36.807L37.033,38.491C37.259,38.592 37.514,38.425 37.514,38.178V34.805C37.514,34.715 37.55,34.628 37.613,34.564L39.768,32.387C39.938,32.216 39.882,31.927 39.66,31.831Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M33.271,36.307L37.086,37.991C37.313,38.092 37.568,37.925 37.568,37.678V34.304C37.568,34.214 37.603,34.128 37.666,34.064L39.797,31.886C39.965,31.714 39.909,31.427 39.688,31.332L37.013,30.204"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M34.233,33.715L36.679,28.777C36.802,28.529 36.7,28.23 36.444,28.124C35.167,27.591 31.442,26.293 25.561,26.293C19.68,26.293 15.955,27.591 14.677,28.124C14.422,28.23 14.32,28.529 14.443,28.777L16.673,33.28C16.793,33.522 17.085,33.629 17.339,33.535C18.388,33.15 21.239,32.278 25.561,32.278C27.787,32.278 29.617,32.505 31.013,32.778"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M19.013,19.204C16.804,19.204 15.013,17.637 15.013,15.704C15.013,13.771 16.804,12.204 19.013,12.204"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M32.013,19.204C34.222,19.204 36.013,17.637 36.013,15.704C36.013,13.771 34.222,12.204 32.013,12.204"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
</vector>
