<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black_80_trans"
    android:clickable="false"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.mimei17.activity.animate.video.widget.videoview.ExoVideoView
        android:id="@+id/reader_video_ad_view"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/iv_bottom"
        app:layout_constraintDimensionRatio="390:250"
        app:layout_constraintTop_toBottomOf="@+id/iv_mute"/>
    <ImageView
        android:id="@+id/iv_ad_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toTopOf="@+id/iv_bottom"
        app:layout_constraintTop_toBottomOf="@+id/iv_mute"/>
    <ImageView
        android:id="@+id/iv_mute"
        android:padding="10dp"
        android:visibility="invisible"
        android:layout_width="@dimen/actionbar_size"
        android:layout_height="@dimen/actionbar_size"
        android:src="@drawable/player_selector_mute_button"
        android:layout_marginStart="5dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/bg_close"
        android:background="@color/black_70_trans"
        android:layout_width="35dp"
        android:layout_height="25dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toStartOf="@+id/reader_ad_buy_button"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Small.Corner"/>
    <ImageView
        android:id="@+id/iv_close"
        android:padding="2dp"
        android:visibility="invisible"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/ic_player_ad_cancel"
        app:layout_constraintBottom_toBottomOf="@+id/bg_close"
        app:layout_constraintEnd_toEndOf="@+id/bg_close"
        app:layout_constraintStart_toStartOf="@+id/bg_close"
        app:layout_constraintTop_toTopOf="@+id/bg_close"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/yellow_496"
        android:gravity="center"
        android:id="@+id/tv_close"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_close"
        app:layout_constraintEnd_toEndOf="@+id/bg_close"
        app:layout_constraintStart_toStartOf="@+id/bg_close"
        app:layout_constraintTop_toTopOf="@+id/bg_close"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="12sp"
        android:textColor="@color/white"
        android:id="@+id/reader_ad_buy_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:minWidth="80dp"
        android:minHeight="25dp"
        android:text="@string/video_player_skip_ad_buy"
        app:backgroundTint="@color/black_70_trans"
        app:cornerRadius="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/white"
        app:strokeWidth="2dp"
        style="@style/Widget.App.Button"/>
    <ImageView
        android:id="@+id/iv_bottom"
        android:background="@drawable/bg_mimei_ad"
        android:paddingLeft="120dp"
        android:paddingRight="120dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/img_mimei_ad"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="390:123"/>
</androidx.constraintlayout.widget.ConstraintLayout>
