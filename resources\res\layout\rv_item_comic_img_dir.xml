<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/img_dir_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/item_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:99"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/Rounded.Image"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/item_bg"
        app:layout_constraintDimensionRatio="103:99"
        app:layout_constraintStart_toStartOf="@+id/item_bg"
        app:layout_constraintTop_toTopOf="@+id/item_bg"
        app:shapeAppearance="@style/Rounded.Left.Image"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/brown_124"
        android:ellipsize="end"
        android:id="@+id/item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="200dp"
        android:maxLines="1"
        android:drawablePadding="10dp"
        android:layout_marginStart="15dp"
        app:drawableStartCompat="@drawable/ic_section_tag_s"
        app:layout_constraintBottom_toTopOf="@+id/item_subtitle"
        app:layout_constraintStart_toEndOf="@+id/item_cover"
        app:layout_constraintTop_toTopOf="@+id/item_cover"
        app:layout_constraintVertical_chainStyle="packed"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:ellipsize="end"
        android:id="@+id/item_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="@+id/item_cover"
        app:layout_constraintStart_toStartOf="@+id/item_title"
        app:layout_constraintTop_toBottomOf="@+id/item_title"/>
    <ImageView
        android:id="@+id/item_vip_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="1dp"
        android:src="@drawable/ic_label_vip"
        app:layout_constraintEnd_toEndOf="@+id/item_bg"
        app:layout_constraintTop_toTopOf="@+id/item_bg"/>
    <ImageView
        android:id="@+id/item_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:src="@drawable/ic_lock"
        app:layout_constraintBottom_toBottomOf="@+id/item_bg"
        app:layout_constraintEnd_toEndOf="@+id/item_bg"/>
    <ImageView
        android:id="@+id/item_outline"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/bg_comic_dir_item"
        app:layout_constraintBottom_toBottomOf="@+id/item_bg"
        app:layout_constraintTop_toTopOf="@+id/item_bg"/>
</androidx.constraintlayout.widget.ConstraintLayout>
