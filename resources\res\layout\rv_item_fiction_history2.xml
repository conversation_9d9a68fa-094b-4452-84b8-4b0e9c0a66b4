<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fiction_item_layout"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/fiction_item_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="175:110"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.fiction.item1"
        app:strokeColor="@color/grey_720"
        app:strokeWidth="1dp"/>
    <TextView
        android:textSize="16sp"
        android:textColor="@color/brown_124"
        android:id="@+id/fiction_item_title"
        android:background="@color/white"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:drawablePadding="10dp"
        app:drawableStartCompat="@drawable/ic_fiction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/fiction_item_desc"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        app:layout_constraintBottom_toTopOf="@+id/fiction_item_read_text"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_title"
        style="@style/Theme.Text.ThreeLine"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="12sp"
        android:textColor="@color/brown_169"
        android:id="@+id/fiction_item_read_text"
        android:paddingLeft="10dp"
        android:paddingTop="5dp"
        android:paddingRight="10dp"
        android:paddingBottom="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:text="@string/collect_text_continue_reading"
        android:layout_marginEnd="5dp"
        android:backgroundTint="@color/grey_720"
        app:icon="@drawable/ic_head_more"
        app:iconGravity="end"
        app:iconPadding="10dp"
        app:iconTint="@color/yellow_478"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/Button.Corner.Large"/>
    <View
        android:id="@+id/guideline"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
