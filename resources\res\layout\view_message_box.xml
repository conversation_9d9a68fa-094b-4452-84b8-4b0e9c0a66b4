<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:background="@color/grey_663"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/box"
        android:background="@drawable/bg_message_box_one_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginBottom="7dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="parent">
        <EditText
            android:textSize="14sp"
            android:textColor="@color/black_153"
            android:id="@+id/message_edittext"
            android:background="@color/transparent"
            android:scrollbars="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxHeight="70dp"
            android:minHeight="36dp"
            android:hint="@string/message_def_hint"
            android:lineSpacingExtra="7dp"
            android:isScrollContainer="true"
            android:paddingStart="20dp"
            android:paddingEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/message_send_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/message_send_btn"
            android:background="?attr/selectableItemBackground"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/ic_send_grey"
            android:minWidth="35dp"
            android:layout_marginEnd="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/message_edittext"
            app:layout_constraintWidth_percent="0.1"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
