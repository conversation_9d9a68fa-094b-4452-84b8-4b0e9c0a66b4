<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:id="@+id/view_guide"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="3:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:id="@+id/top_divider"
        android:background="@color/white_726"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/grey_459"
        android:id="@+id/tv_no_plan_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/purchase_method_empty"
        app:layout_constraintBottom_toTopOf="@+id/btn_update_vip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_divider"
        app:layout_constraintVertical_chainStyle="packed"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="15sp"
        android:textColor="@color/white"
        android:id="@+id/btn_update_vip"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:minWidth="170dp"
        android:text="@string/info_update_vip"
        android:backgroundTint="@color/red_474"
        app:cornerRadius="19dp"
        app:icon="@drawable/ic_btn_star"
        app:iconGravity="textStart"
        app:iconTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="170:38"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_no_plan_msg"
        style="@style/Widget.App.Button"/>
</androidx.constraintlayout.widget.ConstraintLayout>
