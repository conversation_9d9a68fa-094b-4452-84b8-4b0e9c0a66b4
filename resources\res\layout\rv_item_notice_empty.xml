<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="250dp">
    <ImageView
        android:id="@+id/img_empty"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/img_error"
        android:maxWidth="70dp"
        app:layout_constraintBottom_toTopOf="@+id/text_empty"
        app:layout_constraintDimensionRatio="70:56"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintWidth_percent="0.2"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/black_153"
        android:id="@+id/text_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/notice_official_empty"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/img_empty"
        app:layout_constraintStart_toStartOf="@+id/img_empty"
        app:layout_constraintTop_toBottomOf="@+id/img_empty"/>
</androidx.constraintlayout.widget.ConstraintLayout>
