<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="80dp"
    android:width="80dp"
    android:viewportWidth="80"
    android:viewportHeight="80">
    <group>
        <clip-path android:pathData="M0,0h80v80h-80z"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M70.567,65.291C74.859,57.856 72.312,48.35 64.878,44.058C57.444,39.766 47.938,42.313 43.646,49.748C39.354,57.182 41.901,66.688 49.335,70.98C56.769,75.272 66.275,72.725 70.567,65.291Z"/>
        <path
            android:fillColor="#ffeda4"
            android:pathData="M74.681,45.428C79.775,36.603 76.752,25.32 67.928,20.225C59.103,15.13 47.82,18.154 42.725,26.978C37.63,35.802 40.654,47.086 49.478,52.181C58.302,57.276 69.586,54.252 74.681,45.428Z"/>
        <path
            android:fillColor="#ffe57a"
            android:pathData="M51.191,64.512C57.833,53.007 53.891,38.296 42.386,31.653C30.881,25.011 16.17,28.953 9.527,40.458C2.885,51.963 6.827,66.674 18.332,73.317C29.837,79.959 44.548,76.017 51.191,64.512Z"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M17.953,32.653C17.953,42.67 16.491,50.79 39.903,50.79C63.314,50.79 61.852,42.67 61.852,32.653C61.852,22.637 52.025,14.517 39.903,14.517C27.78,14.517 17.953,22.637 17.953,32.653Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M36.17,40L32.945,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M40.708,40L37.482,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M45.245,40L42.019,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#ffd00f"
            android:pathData="M51.663,23.161C55.509,27.007 59.191,29.561 63.039,25.713C66.887,21.865 66.888,15.628 63.042,11.781C59.196,7.935 52.959,7.937 49.111,11.785C45.263,15.633 47.817,19.314 51.663,23.161ZM26.746,23.161C22.9,27.007 19.218,29.561 15.37,25.713C11.522,21.865 11.521,15.628 15.367,11.781C19.213,7.935 25.451,7.937 29.299,11.785C33.147,15.633 30.592,19.314 26.746,23.161Z"
            android:fillType="evenOdd"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M63.039,25.713L63.746,26.42L63.039,25.713ZM51.663,23.161L52.37,22.454L51.663,23.161ZM63.042,11.781L63.749,11.074L63.042,11.781ZM49.111,11.785L49.818,12.492L49.818,12.492L49.111,11.785ZM15.37,25.713L16.077,25.006L15.37,25.713ZM26.746,23.161L27.453,23.868L27.453,23.868L26.746,23.161ZM15.367,11.781L14.66,11.074L15.367,11.781ZM29.299,11.785L30.006,11.078L30.006,11.078L29.299,11.785ZM63.039,25.713L63.746,26.42C62.688,27.478 61.585,28.152 60.426,28.448C59.261,28.745 58.121,28.639 57.029,28.269C54.904,27.548 52.878,25.789 50.956,23.868L51.663,23.161L52.37,22.454C54.294,24.378 56.032,25.819 57.671,26.375C58.462,26.643 59.203,26.695 59.931,26.51C60.665,26.322 61.465,25.872 62.332,25.006L63.039,25.713ZM63.042,11.781L63.749,11.074C67.986,15.311 67.984,22.182 63.746,26.42L63.039,25.713L62.332,25.006C65.789,21.548 65.79,15.944 62.335,12.489L63.042,11.781ZM49.111,11.785L48.403,11.078C52.642,6.839 59.512,6.837 63.749,11.074L63.042,11.781L62.335,12.489C58.88,9.033 53.276,9.034 49.818,12.492L49.111,11.785ZM51.663,23.161L50.956,23.868C49.034,21.946 47.275,19.92 46.554,17.795C46.184,16.703 46.079,15.562 46.376,14.398C46.672,13.239 47.346,12.135 48.403,11.078L49.111,11.785L49.818,12.492C48.951,13.358 48.501,14.158 48.314,14.892C48.128,15.62 48.18,16.362 48.449,17.152C49.005,18.792 50.446,20.529 52.37,22.454L51.663,23.161ZM15.37,25.713L16.077,25.006C16.944,25.872 17.744,26.322 18.478,26.51C19.206,26.695 19.948,26.643 20.738,26.375C22.377,25.819 24.115,24.378 26.039,22.454L26.746,23.161L27.453,23.868C25.531,25.789 23.505,27.548 21.38,28.269C20.288,28.639 19.148,28.745 17.983,28.448C16.824,28.152 15.721,27.478 14.663,26.42L15.37,25.713ZM15.367,11.781L16.074,12.489C12.619,15.944 12.62,21.548 16.077,25.006L15.37,25.713L14.663,26.42C10.425,22.182 10.423,15.311 14.66,11.074L15.367,11.781ZM29.299,11.785L28.591,12.492C25.134,9.034 19.529,9.033 16.074,12.489L15.367,11.781L14.66,11.074C18.897,6.837 25.767,6.839 30.006,11.078L29.299,11.785ZM26.746,23.161L26.039,22.454C27.963,20.529 29.405,18.792 29.961,17.152C30.229,16.362 30.281,15.62 30.095,14.892C29.908,14.158 29.458,13.358 28.591,12.492L29.299,11.785L30.006,11.078C31.063,12.135 31.737,13.239 32.033,14.398C32.33,15.562 32.225,16.703 31.855,17.795C31.134,19.92 29.375,21.946 27.453,23.868L26.746,23.161Z"/>
        <path
            android:fillColor="#954a2c"
            android:pathData="M51.851,49.624C56.834,46.557 56.164,40.467 53.265,36.597C52.542,35.632 50.739,33.964 48.551,31.942C45.799,29.398 42.439,26.292 39.863,23.319C37.287,26.292 33.927,29.398 31.174,31.942C28.987,33.964 27.184,35.632 26.461,36.597C23.562,40.467 22.892,46.557 27.875,49.624C28.124,49.777 15.52,51.093 14.424,40.51C13.452,31.126 19.439,21.61 24.617,15.367C29.299,9.722 36.024,11.003 39.863,12.443C43.702,11.003 50.426,9.722 55.109,15.367C60.287,21.61 66.274,31.126 65.302,40.51C64.206,51.093 51.602,49.777 51.851,49.624Z"
            android:fillType="evenOdd"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M53.265,36.597L52.465,37.196L52.465,37.196L53.265,36.597ZM51.851,49.624L52.375,50.475L52.375,50.475L51.851,49.624ZM48.551,31.942L49.23,31.208L48.551,31.942ZM39.863,23.319L39.107,22.664L39.863,21.791L40.619,22.664L39.863,23.319ZM31.174,31.942L31.853,32.677L31.174,31.942ZM26.461,36.597L27.261,37.196L27.261,37.196L26.461,36.597ZM27.875,49.624L28.399,48.772L27.875,49.624ZM14.424,40.51L13.429,40.613L14.424,40.51ZM24.617,15.367L23.848,14.729L23.848,14.729L24.617,15.367ZM39.863,12.443L40.214,13.379L39.863,13.511L39.512,13.379L39.863,12.443ZM55.109,15.367L54.339,16.006L55.109,15.367ZM65.302,40.51L66.297,40.613L65.302,40.51ZM53.265,36.597L54.065,35.997C55.632,38.088 56.616,40.799 56.513,43.451C56.408,46.142 55.176,48.752 52.375,50.475L51.851,49.624L51.326,48.772C53.509,47.429 54.433,45.46 54.514,43.373C54.597,41.245 53.797,38.975 52.465,37.196L53.265,36.597ZM48.551,31.942L49.23,31.208C50.321,32.216 51.329,33.148 52.154,33.948C52.967,34.737 53.651,35.444 54.065,35.997L53.265,36.597L52.465,37.196C52.156,36.784 51.578,36.176 50.761,35.384C49.957,34.604 48.969,33.69 47.873,32.677L48.551,31.942ZM39.863,23.319L40.619,22.664C43.152,25.587 46.468,28.655 49.23,31.208L48.551,31.942L47.873,32.677C45.13,30.141 41.726,26.996 39.107,23.973L39.863,23.319ZM39.863,23.319L40.619,23.973C38,26.996 34.596,30.141 31.853,32.677L31.174,31.942L30.496,31.208C33.258,28.655 36.574,25.587 39.107,22.664L39.863,23.319ZM31.174,31.942L31.853,32.677C30.757,33.69 29.769,34.604 28.965,35.384C28.148,36.176 27.57,36.784 27.261,37.196L26.461,36.597L25.66,35.997C26.075,35.444 26.759,34.737 27.572,33.948C28.397,33.148 29.405,32.216 30.496,31.208L31.174,31.942ZM26.461,36.597L27.261,37.196C25.929,38.975 25.128,41.245 25.211,43.373C25.293,45.46 26.217,47.429 28.399,48.772L27.875,49.624L27.351,50.475C24.55,48.752 23.318,46.142 23.213,43.451C23.11,40.799 24.094,38.088 25.66,35.997L26.461,36.597ZM27.875,49.624L28.399,48.772C28.488,48.826 28.737,48.998 28.839,49.348C28.959,49.76 28.789,50.091 28.663,50.25C28.551,50.393 28.427,50.47 28.38,50.498C28.319,50.533 28.267,50.556 28.235,50.569C28.125,50.613 28.017,50.635 27.97,50.644C27.844,50.669 27.683,50.689 27.506,50.706C27.143,50.739 26.63,50.763 26.015,50.752C24.787,50.731 23.106,50.571 21.367,50.051C19.629,49.531 17.792,48.637 16.308,47.119C14.811,45.588 13.725,43.472 13.429,40.613L14.424,40.51L15.418,40.407C15.67,42.839 16.571,44.528 17.738,45.721C18.918,46.928 20.422,47.68 21.941,48.135C23.459,48.589 24.95,48.733 26.05,48.753C26.599,48.762 27.038,48.741 27.321,48.714C27.467,48.701 27.553,48.688 27.585,48.682C27.617,48.675 27.566,48.682 27.489,48.713C27.465,48.723 27.419,48.742 27.364,48.775C27.322,48.8 27.202,48.873 27.092,49.013C26.969,49.17 26.8,49.498 26.919,49.907C27.02,50.255 27.267,50.424 27.351,50.475L27.875,49.624ZM14.424,40.51L13.429,40.613C12.918,35.68 14.24,30.768 16.308,26.338C18.378,21.904 21.225,17.891 23.848,14.729L24.617,15.367L25.387,16.006C22.832,19.087 20.095,22.954 18.12,27.184C16.143,31.419 14.958,35.956 15.418,40.407L14.424,40.51ZM24.617,15.367L23.848,14.729C28.969,8.554 36.295,10.036 40.214,11.507L39.863,12.443L39.512,13.379C35.753,11.969 29.63,10.89 25.387,16.006L24.617,15.367ZM55.109,15.367L54.339,16.006C50.096,10.89 43.972,11.969 40.214,13.379L39.863,12.443L39.512,11.507C43.431,10.036 50.757,8.554 55.878,14.729L55.109,15.367ZM65.302,40.51L64.308,40.407C64.768,35.956 63.583,31.419 61.605,27.184C59.631,22.954 56.894,19.087 54.339,16.006L55.109,15.367L55.878,14.729C58.501,17.891 61.348,21.904 63.418,26.338C65.486,30.768 66.808,35.68 66.297,40.613L65.302,40.51ZM51.851,49.624L52.375,50.475C52.459,50.424 52.706,50.255 52.807,49.907C52.926,49.498 52.757,49.17 52.634,49.013C52.524,48.873 52.404,48.8 52.362,48.775C52.307,48.742 52.261,48.723 52.237,48.713C52.16,48.682 52.109,48.675 52.141,48.682C52.173,48.688 52.258,48.701 52.404,48.714C52.688,48.741 53.127,48.762 53.676,48.753C54.776,48.733 56.267,48.589 57.785,48.135C59.304,47.68 60.808,46.928 61.988,45.721C63.155,44.528 64.056,42.839 64.308,40.407L65.302,40.51L66.297,40.613C66.001,43.472 64.915,45.588 63.418,47.119C61.934,48.637 60.097,49.531 58.359,50.051C56.62,50.571 54.938,50.731 53.711,50.752C53.096,50.763 52.583,50.739 52.22,50.706C52.043,50.689 51.882,50.669 51.756,50.644C51.709,50.635 51.601,50.613 51.492,50.569C51.459,50.556 51.407,50.533 51.346,50.498C51.299,50.47 51.175,50.393 51.063,50.25C50.937,50.091 50.767,49.76 50.887,49.348C50.989,48.998 51.238,48.826 51.326,48.772L51.851,49.624Z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M23.192,39L21.5,41L26,39L24.308,41"
            android:strokeColor="#ff8400"
            android:strokeWidth="2"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M53.692,39L52,41L56.5,39L54.808,41"
            android:strokeColor="#ff8400"
            android:strokeWidth="2"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M23,47L56,47A2,2 0,0 1,58 49L58,72A2,2 0,0 1,56 74L23,74A2,2 0,0 1,21 72L21,49A2,2 0,0 1,23 47z"/>
        <path
            android:fillColor="#f6f1e9"
            android:pathData="M39,47H56C57.105,47 58,47.895 58,49V71C58,72.105 57.105,73 56,73H39V47Z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M37.285,51.858C38.062,50.564 39.938,50.564 40.715,51.858L49.183,65.971C49.982,67.304 49.022,69 47.468,69H30.532C28.978,69 28.018,67.304 28.817,65.971L37.285,51.858Z"
            android:strokeColor="#ff4f30"
            android:strokeWidth="2"/>
        <group>
            <clip-path android:pathData="M23,47L56,47A2,2 0,0 1,58 49L58,72A2,2 0,0 1,56 74L23,74A2,2 0,0 1,21 72L21,49A2,2 0,0 1,23 47z"/>
            <path
                android:fillColor="#00000000"
                android:pathData="M23,47L56,47A2,2 0,0 1,58 49L58,72A2,2 0,0 1,56 74L23,74A2,2 0,0 1,21 72L21,49A2,2 0,0 1,23 47z"
                android:strokeColor="#4f200d"
                android:strokeWidth="4.24"/>
        </group>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M20.649,43.659C24.615,43.659 27.875,46.799 27.875,50.725C27.875,54.652 24.616,57.792 20.649,57.792C16.683,57.792 13.423,54.653 13.423,50.725C13.423,46.798 16.683,43.659 20.649,43.659Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M58.135,43.659C62.101,43.659 65.36,46.799 65.36,50.725C65.36,54.652 62.101,57.792 58.135,57.792C54.168,57.792 50.908,54.653 50.908,50.725C50.908,46.798 54.168,43.659 58.135,43.659Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M39,56L39,61"
            android:strokeColor="#ff4f30"
            android:strokeWidth="3"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M39,65.5L39,65.5"
            android:strokeColor="#ff4f30"
            android:strokeWidth="3"
            android:strokeLineCap="round"/>
    </group>
</vector>
