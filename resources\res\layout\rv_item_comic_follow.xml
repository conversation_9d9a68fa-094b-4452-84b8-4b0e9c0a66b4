<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/comic_item_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="120dp">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/comic_item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_title"
        app:layout_constraintDimensionRatio="1:1.49"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <ImageView
        android:id="@+id/comic_item_tag"
        android:visibility="invisible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toTopOf="@+id/comic_item_cover"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/brown_124"
        android:id="@+id/comic_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_info"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_cover"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_cover"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_443"
        android:id="@+id/comic_item_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_cover"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_title"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="10sp"
        android:textColor="@color/orange_387"
        android:id="@+id/comic_item_tag_end"
        android:background="@drawable/bg_end_tag"
        android:paddingLeft="5dp"
        android:paddingTop="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/serialize_end"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_info"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_info"
        app:layout_constraintTop_toTopOf="@+id/comic_item_info"/>
    <TextView
        android:textSize="10sp"
        android:textColor="@color/yellow_438"
        android:id="@+id/comic_item_tag_serial"
        android:background="@drawable/bg_serial_tag"
        android:paddingLeft="5dp"
        android:paddingTop="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/serialize_serial"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_info"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_info"
        app:layout_constraintTop_toTopOf="@+id/comic_item_info"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/bg_comic_item_last_update"
        android:background="@color/black_70_trans"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_last_update"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_last_update"
        app:layout_constraintStart_toStartOf="@+id/comic_item_last_update"
        app:layout_constraintTop_toTopOf="@+id/comic_item_last_update"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/white"
        android:id="@+id/comic_item_last_update"
        android:paddingLeft="5dp"
        android:paddingTop="2dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginStart="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_cover"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"/>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/comic_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="comic_item_cover, comic_item_title, comic_item_info,         comic_item_tag, comic_item_tag_end, comic_item_tag_serial,         comic_item_last_update, bg_comic_item_last_update"/>
    <ImageView
        android:id="@+id/item_default_portrait"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="120:223"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/item_default_landscape"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:elevation="0dp"
        app:cardCornerRadius="15dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:90"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_item_default_landscape"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"/>
    </com.google.android.material.card.MaterialCardView>
</androidx.constraintlayout.widget.ConstraintLayout>
