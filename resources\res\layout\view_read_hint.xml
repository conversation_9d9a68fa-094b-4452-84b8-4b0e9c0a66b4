<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/reader_hint"
    android:background="@color/black_75_trans"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/img_hint"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/img_reader_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="h,1.6:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.constraintlayout.widget.Guideline
        android:orientation="horizontal"
        android:id="@+id/text_guideline_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.49"/>
    <TextView
        android:textSize="17sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:gravity="center"
        android:id="@+id/hint_text"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/text_guideline_bottom"
        app:layout_constraintEnd_toEndOf="@+id/img_hint"
        app:layout_constraintStart_toStartOf="@+id/img_hint"
        app:layout_constraintTop_toTopOf="@+id/img_hint"/>
</androidx.constraintlayout.widget.ConstraintLayout>
