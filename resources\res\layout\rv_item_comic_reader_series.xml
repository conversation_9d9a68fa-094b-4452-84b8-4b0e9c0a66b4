<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/comic_item_layout"
    android:background="@drawable/bg_rv_item_ripple"
    android:padding="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/comic_item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="h, 1:1.48"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.28"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:id="@+id/comic_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="17dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/comic_item_cover"
        app:layout_constraintTop_toTopOf="@+id/comic_item_cover"
        app:layout_constraintVertical_chainStyle="spread_inside"
        style="@style/Theme.Text.TwoLine"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/grey_459"
        android:id="@+id/comic_item_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintEnd_toStartOf="@+id/comic_item_lock"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@+id/comic_item_title"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_title"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/white"
        android:id="@+id/comic_item_record_flag"
        android:background="@color/brown_389"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/comic_reading_tag"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_lock"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/white"
        android:id="@+id/comic_item_vip_flag"
        android:background="@color/pink_444"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/comic_vip_tag"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_lock"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/pink_473"
        android:id="@+id/comic_item_share_flag"
        android:background="@drawable/bg_share_tag"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/comic_share_unlock"
        android:drawablePadding="5dp"
        android:drawableEnd="@drawable/ic_double_arrow_pink"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_record_flag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/comic_item_record_flag"/>
    <ImageView
        android:id="@+id/comic_item_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_lock"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_share_flag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/comic_item_info"
        app:layout_constraintTop_toTopOf="@+id/comic_item_info"/>
</androidx.constraintlayout.widget.ConstraintLayout>
