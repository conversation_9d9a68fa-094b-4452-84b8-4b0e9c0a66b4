<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_gravity="end"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RadioGroup
            android:id="@+id/radioGroup"
            android:background="@color/black_70_trans"
            android:paddingTop="60dp"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_percent="0.46"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
