<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="40dp"
    android:width="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
    <path
        android:fillColor="#3ac2a2"
        android:pathData="M31.521,28.361m-6.881,0a6.881,6.881 0,1 1,13.762 0a6.881,6.881 0,1 1,-13.762 0"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M35.115,26.885L31.534,30.466C31.086,30.914 30.36,30.914 29.912,30.466L27.978,28.532"
        android:strokeColor="#ffffff"
        android:strokeWidth="1.7203"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M11.5,34H24M6,24.5V11.292C6,9.474 7.587,8 9.545,8H30.455C32.413,8 34,9.474 34,11.292V20.037"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M6.75,14.81H31.48"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M19,20H21"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M13,28H15"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M19,28H21"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M25,20H27"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M12.56,23.32C14.3,23.32 15.71,21.91 15.71,20.17C15.71,18.43 14.3,17.02 12.56,17.02C10.82,17.02 9.41,18.43 9.41,20.17C9.41,21.91 10.82,23.32 12.56,23.32Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M11.58,6V9.95"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M19.8,6V9.95"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M28.02,6V9.95"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M11.524,29.541V33.8C11.524,33.967 11.391,34.1 11.224,34.1H2.981C2.815,34.1 2.681,33.967 2.681,33.8V29.541"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M11.524,29.541V33.8C11.524,33.967 11.391,34.1 11.224,34.1H2.981C2.815,34.1 2.681,33.967 2.681,33.8V29.541"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M12.015,27.192H2.191C2.085,27.192 2,27.277 2,27.382V29.35C2,29.455 2.085,29.541 2.191,29.541H12.015C12.12,29.541 12.205,29.455 12.205,29.35V27.382C12.205,27.277 12.12,27.192 12.015,27.192Z"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M7.103,27.192V34.1"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M5.64,27.182C5.04,27.182 4.549,26.696 4.549,26.091C4.549,25.486 5.035,25 5.64,25C6.245,25 6.731,25.486 6.731,26.091V27.192"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M8.565,27.182C9.166,27.182 9.656,26.696 9.656,26.091C9.656,25.491 9.17,25 8.565,25C7.96,25 7.474,25.486 7.474,26.091V27.192"
        android:strokeColor="#684012"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"/>
</vector>
