<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fiction_item_layout"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/fiction_item_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="175:119"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.fiction.item1"
        app:strokeColor="@color/grey_720"
        app:strokeWidth="1dp"/>
    <TextView
        android:textSize="16sp"
        android:textColor="@color/brown_124"
        android:id="@+id/fiction_item_title"
        android:background="@color/white"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:drawablePadding="10dp"
        app:drawableStartCompat="@drawable/ic_fiction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:ellipsize="end"
        android:id="@+id/fiction_item_desc"
        android:padding="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:maxLines="3"
        app:layout_constraintBottom_toTopOf="@+id/fiction_item_like"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_title"/>
    <ImageView
        android:id="@+id/fiction_item_like"
        android:background="@color/white"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_margin="5dp"
        android:src="@drawable/selector_btn_like"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_bg"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_desc"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/fiction_collect_mask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@color/black_50_trans"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_bg"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.fiction.item1"/>
    <CheckBox
        android:id="@+id/ic_collect_marker"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:button="@drawable/selector_rv_item_check"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@+id/fiction_item_bg"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"/>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/collect_view_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="fiction_collect_mask, ic_collect_marker"/>
    <View
        android:id="@+id/guideline"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
