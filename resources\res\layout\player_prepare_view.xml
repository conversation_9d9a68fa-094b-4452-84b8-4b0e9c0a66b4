<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/thumb"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>
    <ImageView
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@color/black_70_trans"/>
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/start_play"
        android:layout_width="@dimen/dkplayer_play_btn_size"
        android:layout_height="@dimen/dkplayer_play_btn_size"
        android:src="@drawable/ic_video_play"/>
    <LinearLayout
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/loading"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/loading_image"
            android:layout_width="@dimen/dkplayer_play_btn_size"
            android:layout_height="@dimen/dkplayer_play_btn_size"/>
        <TextView
            android:textSize="13sp"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/video_play_prepare"/>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/net_warning_layout"
        android:background="@color/black"
        android:focusable="true"
        android:visibility="gone"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textColor="@color/white"
                android:gravity="center"
                android:id="@+id/message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dkplayer_wifi_tip"/>
            <com.google.android.material.button.MaterialButton
                android:textColor="@color/white"
                android:id="@+id/status_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:minHeight="0dp"
                android:text="@string/dkplayer_continue_play"
                android:backgroundTint="@color/yellow_497"
                app:cornerRadius="20dp"
                style="@style/Widget.App.Button"/>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>
