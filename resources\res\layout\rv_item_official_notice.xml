<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@android:color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/notice_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_official_notice"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/notice_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:minHeight="35dp"
        android:maxLines="2"
        android:lineSpacingExtra="5dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="22dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/notice_icon"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:background="@color/grey_687"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/notice_content"
        app:layout_constraintStart_toStartOf="@+id/notice_icon"/>
</androidx.constraintlayout.widget.ConstraintLayout>
