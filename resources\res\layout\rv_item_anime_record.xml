<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/anime_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anime_item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/anime_item_title"
        app:layout_constraintDimensionRatio="176:104"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/item_bottom_mask"
        android:background="@drawable/item_bottom_mask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:alpha="0.8"
        app:layout_constraintBottom_toBottomOf="@+id/anime_item_cover"
        app:layout_constraintDimensionRatio="174:34"
        app:shapeAppearanceOverlay="@style/Rounded.Bottom.Image"/>
    <ImageView
        android:id="@+id/anime_item_tag_new"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"/>
    <TextView
        android:id="@+id/anime_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_cover"
        style="@style/AppTheme.ItemTitleTextView"/>
    <TextView
        android:id="@+id/anime_item_duration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_title"
        style="@style/AppTheme.ItemRankInfoTextView"/>
    <TextView
        android:textSize="12sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:id="@+id/record_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/item_bottom_mask"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/item_bottom_mask"/>
</androidx.constraintlayout.widget.ConstraintLayout>
