<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/center_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/bg_center_container"
            android:background="@color/black_70_trans"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <ImageView
            android:id="@+id/iv_play_rewind"
            android:padding="6dp"
            android:layout_width="@dimen/dkplayer_controller_height"
            android:layout_height="@dimen/dkplayer_controller_height"
            android:src="@drawable/ic_player_action_back_15sec"
            android:layout_marginEnd="80dp"
            app:layout_constraintBottom_toBottomOf="@+id/iv_play"
            app:layout_constraintEnd_toStartOf="@+id/iv_play"
            app:layout_constraintTop_toTopOf="@+id/iv_play"/>
        <ImageView
            android:id="@+id/iv_play"
            android:padding="3dp"
            android:layout_width="@dimen/dkplayer_controller_height"
            android:layout_height="@dimen/dkplayer_controller_height"
            android:src="@drawable/player_selector_play_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:id="@+id/iv_play_forward"
            android:padding="6dp"
            android:layout_width="@dimen/dkplayer_controller_height"
            android:layout_height="@dimen/dkplayer_controller_height"
            android:src="@drawable/ic_player_action_forward_15sec"
            android:layout_marginStart="80dp"
            app:layout_constraintBottom_toBottomOf="@+id/iv_play"
            app:layout_constraintStart_toEndOf="@+id/iv_play"
            app:layout_constraintTop_toTopOf="@+id/iv_play"/>
        <TextView
            android:textSize="12sp"
            android:textColor="@color/white"
            android:id="@+id/curr_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/seekBar"
            app:layout_constraintStart_toStartOf="@+id/seekBar"/>
        <TextView
            android:textSize="12sp"
            android:textColor="@color/white"
            android:id="@+id/time_divider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=" / "
            app:layout_constraintBottom_toBottomOf="@+id/curr_time"
            app:layout_constraintStart_toEndOf="@+id/curr_time"/>
        <TextView
            android:textSize="12sp"
            android:textColor="@color/white"
            android:id="@+id/total_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/curr_time"
            app:layout_constraintStart_toEndOf="@+id/time_divider"/>
        <SeekBar
            android:id="@+id/seekBar"
            android:paddingTop="5dp"
            android:paddingBottom="10dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxHeight="3dp"
            android:max="1000"
            android:progressDrawable="@drawable/player_layer_progress_bar"
            android:minHeight="3dp"
            android:thumb="@drawable/player_seekbar_thumb"
            android:thumbOffset="0dp"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:layout_marginStart="15dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/fullscreen"
            app:layout_constraintStart_toStartOf="parent"/>
        <ImageView
            android:id="@+id/fullscreen"
            android:padding="@dimen/dkplayer_controller_icon_padding"
            android:layout_width="@dimen/dkplayer_controller_height"
            android:layout_height="@dimen/dkplayer_controller_height"
            android:src="@drawable/player_selector_full_screen_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout
        android:layout_gravity="bottom"
        android:id="@+id/bottom_container"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_gravity="bottom"
            android:id="@+id/bottom_progress_background"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="8dp"/>
        <SeekBar
            android:id="@+id/bottom_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="2dp"
            android:max="1000"
            android:progressDrawable="@drawable/player_layer_progress_bar"
            android:minHeight="2dp"
            android:thumbOffset="0dp"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:splitTrack="false"
            style="?android:attr/progressBarStyleHorizontal"/>
    </FrameLayout>
</FrameLayout>
