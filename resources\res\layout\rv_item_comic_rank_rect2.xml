<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/comic_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/comic_item_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_title"
        app:layout_constraintDimensionRatio="1:1.47"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/brown_124"
        android:id="@+id/comic_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintBottom_toTopOf="@+id/comic_item_info"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_cover"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_cover"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_443"
        android:id="@+id/comic_item_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toBottomOf="@+id/comic_item_title"
        style="@style/Theme.Text.OneLine"/>
    <TextView
        android:textSize="10sp"
        android:textColor="@color/orange_387"
        android:id="@+id/comic_item_tag_end"
        android:background="@drawable/bg_end_tag"
        android:paddingLeft="5dp"
        android:paddingTop="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/serialize_end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <TextView
        android:textSize="10sp"
        android:textColor="@color/yellow_438"
        android:id="@+id/comic_item_tag_serial"
        android:background="@drawable/bg_serial_tag"
        android:paddingLeft="5dp"
        android:paddingTop="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/serialize_serial"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <ImageView
        android:id="@+id/comic_item_tag_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/comic_item_cover"
        app:layout_constraintTop_toTopOf="@+id/comic_item_cover"/>
    <ImageView
        android:id="@+id/comic_item_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:src="@drawable/selector_btn_like"
        android:layout_marginEnd="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/comic_item_cover"
        app:layout_constraintEnd_toEndOf="@+id/comic_item_cover"/>
</androidx.constraintlayout.widget.ConstraintLayout>
