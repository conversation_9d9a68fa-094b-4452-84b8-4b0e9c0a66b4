<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@+id/title_container"
        android:background="@drawable/mask_home_banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/actionbar_size">
        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"/>
        <ImageView
            android:id="@+id/iv_battery"
            android:layout_width="26dp"
            android:layout_height="20dp"
            android:src="@drawable/dkplayer_battery_level"/>
        <TextView
            android:textColor="@color/white"
            android:gravity="end"
            android:id="@+id/sys_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"/>
        <com.google.android.material.button.MaterialButton
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:id="@+id/iv_speed"
            android:background="@null"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="倍速 X1.0"
            android:layout_marginEnd="5dp"
            android:backgroundTint="@color/transparent"
            app:strokeColor="@color/white"
            app:strokeWidth="2dp"
            style="@style/Button.Corner"/>
        <ImageView
            android:id="@+id/iv_mute"
            android:padding="@dimen/dkplayer_controller_icon_padding"
            android:layout_width="@dimen/dkplayer_controller_height"
            android:layout_height="@dimen/dkplayer_controller_height"
            android:src="@drawable/player_selector_mute_button"/>
    </LinearLayout>
</FrameLayout>
