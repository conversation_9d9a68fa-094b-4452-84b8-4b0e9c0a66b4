<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/msg_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="15dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.08"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/brown_389"
        android:id="@+id/msg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/official_message_title"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/msg_icon"
        app:layout_constraintStart_toEndOf="@+id/msg_icon"
        app:layout_constraintTop_toTopOf="@+id/msg_icon"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/black_153"
        android:autoLink="email|web"
        android:id="@+id/msg_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:lineSpacingExtra="6dp"
        app:layout_constraintStart_toStartOf="@+id/msg_title"
        app:layout_constraintTop_toBottomOf="@+id/msg_title"
        app:layout_constraintWidth_percent="0.67"/>
    <View
        android:background="@color/white_726"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/msg_icon"
        app:layout_constraintTop_toBottomOf="@+id/msg_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>
