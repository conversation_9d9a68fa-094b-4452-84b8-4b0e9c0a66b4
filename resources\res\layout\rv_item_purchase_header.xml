<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="44dp">
    <View
        android:id="@+id/header_divider"
        android:background="@color/white_726"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:gravity="center_vertical"
        android:id="@+id/tv_purchase_header_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_divider"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/yellow_496"
        android:gravity="center"
        android:id="@+id/tv_purchase_header_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/purchase_method_going_hint"
        android:drawablePadding="5dp"
        android:drawableEnd="@drawable/ic_history_ing"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_divider"/>
</androidx.constraintlayout.widget.ConstraintLayout>
