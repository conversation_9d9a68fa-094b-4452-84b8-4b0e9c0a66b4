<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="390:250"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.constraintlayout.widget.Guideline
        android:orientation="vertical"
        android:id="@+id/guideline_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.05"/>
    <androidx.constraintlayout.widget.Guideline
        android:orientation="vertical"
        android:id="@+id/guideline_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.95"/>
    <ImageView
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:src="@drawable/player_gesture_line"
        app:layout_constraintBottom_toTopOf="@+id/iv_mimei"
        app:layout_constraintEnd_toEndOf="@+id/iv_mimei"
        app:layout_constraintStart_toStartOf="@+id/iv_mimei"
        app:layout_constraintTop_toTopOf="parent"/>
    <include
        android:id="@+id/gesture_brightness"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="@+id/guideline_1"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/item_player_gesture_vertical"/>
    <include
        android:id="@+id/player_hint"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/item_player_hint"/>
    <include
        android:id="@+id/player_hint_forward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/gesture_brightness"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/gesture_brightness"
        app:layout_constraintWidth_percent="0.27"
        layout="@layout/item_player_gesture_horizontal"/>
    <include
        android:id="@+id/gesture_volume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/guideline"
        app:layout_constraintEnd_toEndOf="@+id/guideline_2"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/item_player_gesture_vertical"/>
    <ImageView
        android:id="@+id/iv_mimei"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/img_player_gesture_mimei"
        app:layout_constraintDimensionRatio="109:115"
        app:layout_constraintEnd_toEndOf="@+id/player_hint_forward"
        app:layout_constraintStart_toStartOf="@+id/player_hint_forward"
        app:layout_constraintTop_toBottomOf="@+id/player_hint_forward"
        app:layout_constraintWidth_percent="0.25"/>
    <ImageView
        android:id="@+id/close_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="120dp"
        android:src="@drawable/ic_close_pop_ad"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
