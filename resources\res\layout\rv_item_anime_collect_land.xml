<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/anime_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anime_item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/anime_item_title"
        app:layout_constraintDimensionRatio="176:104"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:id="@+id/anime_item_info"
        android:background="@drawable/bg_item_info"
        android:paddingLeft="5dp"
        android:paddingTop="2dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        app:layout_constraintEnd_toEndOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"
        style="@style/AppTheme.AnimateItemInfoTextView"/>
    <TextView
        android:id="@+id/anime_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_cover"
        style="@style/AppTheme.ItemTitleTextView"/>
    <ImageView
        android:id="@+id/anime_item_tag_new"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"/>
    <ImageView
        android:id="@+id/anime_item_like"
        android:visibility="gone"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/selector_btn_like"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/anime_item_cover"
        app:layout_constraintEnd_toEndOf="@+id/anime_item_cover"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anime_collect_mask"
        android:background="@color/black_50_trans"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <CheckBox
        android:id="@+id/ic_collect_marker"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:button="@drawable/selector_rv_item_check"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"/>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/collect_view_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="anime_collect_mask, ic_collect_marker"/>
</androidx.constraintlayout.widget.ConstraintLayout>
