<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="0dp"
    app:elevation="0dp">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:background="@color/yellow_496"
        android:layout_width="match_parent"
        android:layout_height="@dimen/actionbar_size"
        app:contentInsetStart="0dp"
        app:layout_constraintTop_toTopOf="parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ImageButton
                android:id="@+id/btnBack"
                android:background="@null"
                android:padding="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_arrow_back"
                android:layout_centerVertical="true"/>
            <TextView
                android:textSize="17sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:gravity="center"
                android:id="@+id/toolbarTitle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>
</com.google.android.material.appbar.AppBarLayout>
