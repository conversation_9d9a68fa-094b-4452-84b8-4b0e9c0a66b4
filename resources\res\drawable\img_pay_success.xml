<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="80dp"
    android:width="81dp"
    android:viewportWidth="81"
    android:viewportHeight="80">
    <group>
        <clip-path android:pathData="M0.5,0h80v80h-80z"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M71.067,65.291C75.359,57.856 72.812,48.35 65.378,44.058C57.944,39.766 48.438,42.313 44.146,49.748C39.854,57.182 42.401,66.688 49.835,70.98C57.269,75.272 66.775,72.725 71.067,65.291Z"/>
        <path
            android:fillColor="#ffeda4"
            android:pathData="M75.181,45.428C80.275,36.603 77.252,25.32 68.428,20.225C59.603,15.13 48.32,18.154 43.225,26.978C38.13,35.802 41.154,47.086 49.978,52.181C58.802,57.276 70.086,54.252 75.181,45.428Z"/>
        <path
            android:fillColor="#ffe57a"
            android:pathData="M51.691,64.512C58.333,53.007 54.391,38.296 42.886,31.653C31.381,25.011 16.67,28.953 10.027,40.458C3.385,51.963 7.327,66.674 18.832,73.317C30.337,79.959 45.048,76.017 51.691,64.512Z"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M18.453,32.653C18.453,42.67 16.991,50.79 40.403,50.79C63.814,50.79 62.352,42.67 62.352,32.653C62.352,22.637 52.525,14.517 40.403,14.517C28.28,14.517 18.453,22.637 18.453,32.653Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M37.67,40L34.445,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M42.208,40L38.982,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M46.745,40L43.52,44.613"
            android:strokeColor="#ffba71"
            android:strokeWidth="2"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#ffd00f"
            android:pathData="M63.542,11.782C59.696,7.936 53.458,7.937 49.61,11.785C45.762,15.633 48.317,19.314 52.163,23.161C56.009,27.007 59.691,29.561 63.539,25.713C67.387,21.865 67.388,15.628 63.542,11.782ZM29.799,11.785C25.951,7.937 19.713,7.936 15.867,11.782C12.021,15.628 12.022,21.865 15.87,25.713C19.718,29.561 23.4,27.007 27.246,23.161C31.092,19.314 33.647,15.633 29.799,11.785Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M63.542,11.782L64.249,11.075L64.249,11.075L63.542,11.782ZM49.61,11.785L48.903,11.078L48.903,11.078L49.61,11.785ZM52.163,23.161L51.456,23.868L52.163,23.161ZM63.539,25.713L64.246,26.42L64.246,26.42L63.539,25.713ZM29.799,11.785L30.506,11.078L30.506,11.078L29.799,11.785ZM15.867,11.782L15.16,11.075L15.16,11.075L15.867,11.782ZM15.87,25.713L15.163,26.42L15.163,26.42L15.87,25.713ZM27.246,23.161L27.953,23.868L27.246,23.161ZM63.542,11.782L62.835,12.489C59.38,9.033 53.775,9.034 50.317,12.492L49.61,11.785L48.903,11.078C53.141,6.839 60.012,6.838 64.249,11.075L63.542,11.782ZM49.61,11.785L50.317,12.492C49.451,13.358 49.001,14.158 48.814,14.892C48.628,15.62 48.68,16.362 48.948,17.152C49.504,18.791 50.946,20.529 52.87,22.454L52.163,23.161L51.456,23.868C49.534,21.946 47.775,19.92 47.054,17.795C46.684,16.702 46.579,15.562 46.876,14.397C47.172,13.238 47.846,12.135 48.903,11.078L49.61,11.785ZM52.163,23.161L52.87,22.454C54.794,24.378 56.532,25.819 58.172,26.375C58.962,26.643 59.704,26.696 60.432,26.51C61.166,26.323 61.965,25.873 62.832,25.006L63.539,25.713L64.246,26.42C63.189,27.478 62.085,28.152 60.926,28.448C59.762,28.745 58.621,28.64 57.529,28.269C55.404,27.549 53.378,25.789 51.456,23.868L52.163,23.161ZM63.539,25.713L62.832,25.006C66.29,21.548 66.29,15.944 62.835,12.489L63.542,11.782L64.249,11.075C68.486,15.311 68.484,22.182 64.246,26.42L63.539,25.713ZM29.799,11.785L29.092,12.492C25.634,9.034 20.03,9.033 16.574,12.489L15.867,11.782L15.16,11.075C19.397,6.838 26.268,6.839 30.506,11.078L29.799,11.785ZM15.867,11.782L16.574,12.489C13.119,15.944 13.12,21.548 16.577,25.006L15.87,25.713L15.163,26.42C10.925,22.182 10.923,15.311 15.16,11.075L15.867,11.782ZM15.87,25.713L16.577,25.006C17.444,25.873 18.244,26.323 18.977,26.51C19.705,26.696 20.447,26.643 21.237,26.375C22.877,25.819 24.615,24.378 26.539,22.454L27.246,23.161L27.953,23.868C26.031,25.789 24.005,27.549 21.88,28.269C20.788,28.64 19.647,28.745 18.483,28.448C17.324,28.152 16.221,27.478 15.163,26.42L15.87,25.713ZM27.246,23.161L26.539,22.454C28.463,20.529 29.905,18.791 30.461,17.152C30.729,16.362 30.781,15.62 30.595,14.892C30.408,14.158 29.958,13.358 29.092,12.492L29.799,11.785L30.506,11.078C31.563,12.135 32.237,13.238 32.533,14.397C32.83,15.562 32.725,16.702 32.355,17.795C31.634,19.92 29.875,21.946 27.953,23.868L27.246,23.161Z"/>
        <path
            android:fillColor="#954a2c"
            android:pathData="M40.364,12.443C36.525,11.003 29.8,9.722 25.118,15.367C19.939,21.61 13.952,31.126 14.923,40.51C16.019,51.089 28.616,49.778 28.375,49.624C23.392,46.558 24.062,40.467 26.96,36.597C28.593,34.417 35.74,28.654 40.363,23.318C44.985,28.654 52.132,34.417 53.765,36.597C56.664,40.467 57.334,46.558 52.35,49.624C52.118,49.779 64.707,51.086 65.802,40.51C66.774,31.126 60.787,21.61 55.609,15.367C50.927,9.723 44.202,11.003 40.364,12.443Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M40.364,12.443L40.715,13.38L40.364,13.512L40.012,13.38L40.364,12.443ZM25.118,15.367L24.348,14.729L24.348,14.729L25.118,15.367ZM14.923,40.51L13.929,40.613L13.929,40.613L14.923,40.51ZM28.375,49.624L28.899,48.772L28.907,48.777L28.915,48.782L28.375,49.624ZM26.96,36.597L26.16,35.997L26.16,35.997L26.96,36.597ZM40.363,23.318L39.607,22.663L40.363,21.791L41.118,22.663L40.363,23.318ZM53.765,36.597L54.565,35.997L54.565,35.997L53.765,36.597ZM52.35,49.624L51.795,48.792L51.81,48.782L51.826,48.772L52.35,49.624ZM65.802,40.51L66.797,40.613L66.797,40.613L65.802,40.51ZM55.609,15.367L56.379,14.729L56.379,14.729L55.609,15.367ZM40.364,12.443L40.012,13.38C36.254,11.969 30.13,10.89 25.887,16.006L25.118,15.367L24.348,14.729C29.469,8.554 36.796,10.036 40.715,11.507L40.364,12.443ZM25.118,15.367L25.887,16.006C23.332,19.086 20.596,22.954 18.62,27.184C16.643,31.419 15.457,35.956 15.918,40.407L14.923,40.51L13.929,40.613C13.418,35.68 14.74,30.768 16.808,26.338C18.878,21.904 21.725,17.891 24.348,14.729L25.118,15.367ZM14.923,40.51L15.918,40.407C16.17,42.838 17.07,44.526 18.236,45.72C19.416,46.927 20.919,47.679 22.437,48.134C23.955,48.589 25.446,48.733 26.547,48.753C27.096,48.763 27.535,48.741 27.819,48.715C27.965,48.701 28.052,48.688 28.084,48.682C28.116,48.676 28.067,48.682 27.992,48.712C27.969,48.721 27.925,48.74 27.871,48.771C27.83,48.794 27.716,48.863 27.607,48.994C27.491,49.135 27.307,49.449 27.406,49.862C27.494,50.227 27.746,50.409 27.836,50.466L28.375,49.624L28.915,48.782C29.008,48.842 29.263,49.027 29.351,49.394C29.451,49.809 29.266,50.126 29.147,50.27C29.037,50.404 28.918,50.475 28.872,50.503C28.813,50.536 28.761,50.558 28.73,50.571C28.621,50.614 28.515,50.635 28.467,50.645C28.341,50.669 28.181,50.69 28.003,50.706C27.64,50.74 27.127,50.763 26.511,50.752C25.283,50.731 23.602,50.57 21.863,50.05C20.126,49.529 18.289,48.635 16.806,47.118C15.309,45.586 14.225,43.471 13.929,40.613L14.923,40.51ZM28.375,49.624L27.851,50.476C25.05,48.752 23.818,46.143 23.713,43.451C23.609,40.799 24.594,38.088 26.16,35.997L26.96,36.597L27.761,37.196C26.428,38.975 25.628,41.244 25.711,43.373C25.793,45.461 26.717,47.43 28.899,48.772L28.375,49.624ZM26.96,36.597L26.16,35.997C26.625,35.377 27.436,34.554 28.402,33.63C29.381,32.693 30.583,31.591 31.861,30.407C34.432,28.025 37.336,25.284 39.607,22.663L40.363,23.318L41.118,23.973C38.767,26.688 35.786,29.497 33.22,31.875C31.929,33.071 30.749,34.153 29.785,35.075C28.806,36.012 28.112,36.727 27.761,37.196L26.96,36.597ZM40.363,23.318L41.118,22.663C43.389,25.284 46.294,28.025 48.865,30.407C50.143,31.591 51.344,32.693 52.324,33.63C53.289,34.554 54.101,35.377 54.565,35.997L53.765,36.597L52.965,37.196C52.613,36.727 51.919,36.012 50.941,35.075C49.977,34.152 48.797,33.071 47.506,31.875C44.939,29.497 41.959,26.688 39.607,23.973L40.363,23.318ZM53.765,36.597L54.565,35.997C56.132,38.088 57.116,40.799 57.013,43.451C56.908,46.143 55.675,48.752 52.874,50.476L52.35,49.624L51.826,48.772C54.008,47.43 54.933,45.461 55.014,43.373C55.097,41.244 54.297,38.975 52.965,37.196L53.765,36.597ZM52.35,49.624L52.905,50.456C53.002,50.391 53.257,50.195 53.329,49.815C53.408,49.402 53.212,49.102 53.103,48.976C52.997,48.854 52.888,48.789 52.847,48.766C52.796,48.737 52.753,48.719 52.73,48.71C52.657,48.682 52.61,48.676 52.641,48.682C52.675,48.688 52.762,48.701 52.908,48.715C53.193,48.741 53.633,48.762 54.182,48.753C55.283,48.733 56.774,48.588 58.291,48.133C59.809,47.678 61.311,46.925 62.49,45.719C63.656,44.525 64.556,42.837 64.808,40.407L65.802,40.51L66.797,40.613C66.501,43.47 65.417,45.585 63.921,47.116C62.439,48.634 60.603,49.528 58.866,50.049C57.128,50.57 55.447,50.73 54.218,50.752C53.603,50.763 53.089,50.74 52.725,50.706C52.547,50.69 52.386,50.669 52.26,50.645C52.212,50.636 52.107,50.614 52,50.573C51.969,50.56 51.919,50.539 51.862,50.507C51.816,50.481 51.703,50.413 51.594,50.288C51.482,50.16 51.285,49.857 51.365,49.44C51.438,49.058 51.695,48.859 51.795,48.792L52.35,49.624ZM65.802,40.51L64.808,40.407C65.268,35.956 64.082,31.419 62.106,27.184C60.131,22.954 57.394,19.086 54.839,16.006L55.609,15.367L56.379,14.729C59.001,17.891 61.848,21.904 63.918,26.338C65.986,30.768 67.308,35.68 66.797,40.613L65.802,40.51ZM55.609,15.367L54.839,16.006C50.597,10.891 44.473,11.969 40.715,13.38L40.364,12.443L40.012,11.507C43.931,10.036 51.257,8.555 56.379,14.729L55.609,15.367Z"/>
        <path
            android:fillColor="#ffe063"
            android:pathData="M51.847,49H30.021C28.353,49 27,50.353 27,52.021V63.54C27,65.209 28.353,66.561 30.021,66.561H51.847C53.516,66.561 54.868,65.209 54.868,63.54V52.021C54.868,50.353 53.516,49 51.847,49Z"/>
        <path
            android:fillColor="#ffd00f"
            android:pathData="M51.969,51.415H32.326C30.719,51.415 29.416,52.719 29.416,54.326V63.662C29.416,65.27 30.719,66.573 32.326,66.573H51.969C53.577,66.573 54.88,65.27 54.88,63.662V54.326C54.88,52.719 53.577,51.415 51.969,51.415Z"/>
        <path
            android:fillColor="#ea6068"
            android:pathData="M50.029,64.146C51.033,64.146 51.847,63.332 51.847,62.328C51.847,61.324 51.033,60.51 50.029,60.51C49.025,60.51 48.211,61.324 48.211,62.328C48.211,63.332 49.025,64.146 50.029,64.146Z"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M47.966,64.146C48.97,64.146 49.784,63.332 49.784,62.328C49.784,61.324 48.97,60.51 47.966,60.51C46.962,60.51 46.148,61.324 46.148,62.328C46.148,63.332 46.962,64.146 47.966,64.146Z"
            android:fillAlpha="0.7"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M51.847,48C54.068,48 55.868,49.801 55.868,52.021V63.54C55.868,65.761 54.068,67.562 51.847,67.562H30.021C27.801,67.562 26,65.761 26,63.54V52.021L26.005,51.814C26.112,49.69 27.87,48 30.021,48H51.847Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M33.453,55.702H31.233C30.698,55.702 30.265,56.136 30.265,56.671V58.891C30.265,59.426 30.698,59.859 31.233,59.859H33.453C33.988,59.859 34.421,59.426 34.421,58.891V56.671C34.421,56.136 33.988,55.702 33.453,55.702Z"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M21.149,43.659C25.115,43.659 28.375,46.799 28.375,50.725C28.375,54.652 25.116,57.792 21.149,57.792C17.183,57.792 13.923,54.653 13.923,50.725C13.923,46.798 17.183,43.659 21.149,43.659Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M58.635,43.659C62.601,43.659 65.86,46.799 65.86,50.725C65.86,54.652 62.601,57.792 58.635,57.792C54.668,57.792 51.408,54.653 51.408,50.725C51.408,46.798 54.668,43.659 58.635,43.659Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="2"/>
    </group>
</vector>
