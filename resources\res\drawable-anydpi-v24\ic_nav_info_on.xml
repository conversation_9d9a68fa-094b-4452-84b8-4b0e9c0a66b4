<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="25dp"
    android:width="25dp"
    android:viewportWidth="25"
    android:viewportHeight="25">
    <path
        android:fillColor="#ffd00f"
        android:pathData="M10.593,18H14.406C17.663,18 20.444,20.051 21.522,22.935L21.621,23.217C21.682,23.403 21.642,23.566 21.517,23.711C21.379,23.871 21.135,24 20.838,24H4.162C3.865,24 3.62,23.871 3.482,23.711C3.372,23.584 3.329,23.443 3.361,23.285L3.379,23.217C4.377,20.186 7.231,18 10.593,18Z"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M19.5,9L19.5,9A3.5,3.5 0,0 0,23 5.5L23,5.5A3.5,3.5 0,0 0,19.5 2L19.5,2A3.5,3.5 0,0 0,16 5.5L16,5.5A3.5,3.5 0,0 0,19.5 9z"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#ffd00f"
        android:pathData="M5.5,9L5.5,9A3.5,3.5 0,0 0,9 5.5L9,5.5A3.5,3.5 0,0 0,5.5 2L5.5,2A3.5,3.5 0,0 0,2 5.5L2,5.5A3.5,3.5 0,0 0,5.5 9z"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#4f200d"
        android:pathData="M11,3L14,3A8,8 0,0 1,22 11L22,11A8,8 0,0 1,14 19L11,19A8,8 0,0 1,3 11L3,11A8,8 0,0 1,11 3z"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#fffae4"
        android:pathData="M12,6L13,6A7,7 0,0 1,20 13L20,13A7,7 0,0 1,13 20L12,20A7,7 0,0 1,5 13L5,13A7,7 0,0 1,12 6z"
        android:strokeColor="#4f200d"
        android:strokeWidth="2"/>
    <path
        android:fillColor="#4f200d"
        android:pathData="M13.945,5.055C13.981,5.365 14,5.68 14,6C14,10.418 10.418,14 6,14C5.322,14 4.664,13.916 4.035,13.757C4.012,13.508 4,13.255 4,13C4,8.582 7.582,5 12,5H13C13.32,5 13.635,5.019 13.945,5.055Z"
        android:fillType="evenOdd"/>
    <group>
        <clip-path
            android:pathData="M13.945,5.055C13.981,5.365 14,5.68 14,6C14,10.418 10.418,14 6,14C5.322,14 4.664,13.916 4.035,13.757C4.012,13.508 4,13.255 4,13C4,8.582 7.582,5 12,5H13C13.32,5 13.635,5.019 13.945,5.055Z"
            android:fillType="evenOdd"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M13.945,5.055L15.931,4.822L15.747,3.253L14.179,3.069L13.945,5.055ZM4.035,13.757L2.044,13.944L2.176,15.35L3.546,15.696L4.035,13.757ZM11.958,5.289C11.986,5.521 12,5.759 12,6H16C16,5.602 15.977,5.209 15.931,4.822L11.958,5.289ZM12,6C12,9.314 9.314,12 6,12V16C11.523,16 16,11.523 16,6H12ZM6,12C5.489,12 4.995,11.936 4.525,11.818L3.546,15.696C4.333,15.895 5.156,16 6,16V12ZM6.027,13.57C6.009,13.383 6,13.193 6,13H2C2,13.318 2.015,13.633 2.044,13.944L6.027,13.57ZM6,13C6,9.686 8.686,7 12,7V3C6.477,3 2,7.477 2,13H6ZM12,7H13V3H12V7ZM13,7C13.241,7 13.479,7.014 13.711,7.042L14.179,3.069C13.791,3.023 13.398,3 13,3V7Z"/>
    </group>
    <path
        android:fillColor="#4f200d"
        android:pathData="M20.956,14.156C20.985,13.908 21,13.656 21,13.4C21,9.865 18.135,7 14.6,7C14.058,7 13.531,7.067 13.028,7.194C13.01,7.394 13,7.596 13,7.8C13,11.335 15.865,14.2 19.4,14.2H20.2C20.456,14.2 20.708,14.185 20.956,14.156Z"
        android:fillType="evenOdd"/>
    <group>
        <clip-path
            android:pathData="M20.956,14.156C20.985,13.908 21,13.656 21,13.4C21,9.865 18.135,7 14.6,7C14.058,7 13.531,7.067 13.028,7.194C13.01,7.394 13,7.596 13,7.8C13,11.335 15.865,14.2 19.4,14.2H20.2C20.456,14.2 20.708,14.185 20.956,14.156Z"
            android:fillType="evenOdd"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M20.956,14.156L22.942,14.39L22.758,15.958L21.19,16.142L20.956,14.156ZM13.028,7.194L11.037,7.008L11.169,5.601L12.539,5.255L13.028,7.194ZM18.969,13.922C18.99,13.752 19,13.578 19,13.4H23C23,13.734 22.98,14.064 22.942,14.39L18.969,13.922ZM19,13.4C19,10.97 17.03,9 14.6,9V5C19.239,5 23,8.761 23,13.4H19ZM14.6,9C14.224,9 13.862,9.047 13.518,9.134L12.539,5.255C13.2,5.088 13.891,5 14.6,5V9ZM15.019,7.381C15.007,7.519 15,7.658 15,7.8H11C11,7.533 11.012,7.269 11.037,7.008L15.019,7.381ZM15,7.8C15,10.23 16.97,12.2 19.4,12.2V16.2C14.761,16.2 11,12.439 11,7.8H15ZM19.4,12.2H20.2V16.2H19.4V12.2ZM20.2,12.2C20.378,12.2 20.552,12.19 20.722,12.17L21.19,16.142C20.864,16.18 20.534,16.2 20.2,16.2V12.2Z"/>
    </group>
    <path
        android:fillColor="#00000000"
        android:pathData="M8,15H6"
        android:strokeColor="#ff8400"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M19,15H17"
        android:strokeColor="#ff8400"
        android:strokeWidth="2"
        android:strokeLineCap="round"/>
</vector>
