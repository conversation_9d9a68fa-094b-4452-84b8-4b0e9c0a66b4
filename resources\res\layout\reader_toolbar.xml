<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/reader_toolbar"
    android:background="@color/black_90_trans"
    android:visibility="visible"
    android:layout_width="match_parent"
    android:layout_height="@dimen/actionbar_size">
    <ImageButton
        android:id="@+id/reader_back_btn"
        android:background="@null"
        android:padding="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_back"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/reader_toolbar"
        app:layout_constraintEnd_toStartOf="@+id/reader_chapter_title"
        app:layout_constraintStart_toStartOf="@+id/reader_toolbar"
        app:layout_constraintTop_toTopOf="@+id/reader_toolbar"/>
    <TextView
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:gravity="center"
        android:id="@+id/reader_chapter_title"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="@+id/reader_toolbar"
        app:layout_constraintEnd_toStartOf="@+id/reader_share"
        app:layout_constraintStart_toEndOf="@+id/reader_back_btn"
        app:layout_constraintTop_toTopOf="@+id/reader_toolbar"
        style="@style/Theme.Text.OneLine"/>
    <ImageView
        android:id="@+id/reader_share"
        android:padding="10dp"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/ic_share"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/reader_toolbar"
        app:layout_constraintEnd_toEndOf="@+id/reader_toolbar"
        app:layout_constraintStart_toEndOf="@+id/reader_chapter_title"
        app:layout_constraintTop_toTopOf="@+id/reader_toolbar"/>
</androidx.constraintlayout.widget.ConstraintLayout>
