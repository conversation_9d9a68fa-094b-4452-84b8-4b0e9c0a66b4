<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/tabLayout_size"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/CollectFilterTabLayout"/>
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/edit_btn_shadow"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/edit_btn"
        app:layout_constraintStart_toEndOf="@+id/tab_layout"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="11sp"
        android:textColor="@color/white"
        android:id="@+id/edit_btn"
        android:padding="0dp"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:minWidth="45dp"
        android:text="@string/button_action_edit"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:backgroundTint="@color/grey_579"
        app:cornerRadius="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/edit_btn_shadow"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
