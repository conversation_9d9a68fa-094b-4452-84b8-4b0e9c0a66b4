<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="115dp"
    android:width="109dp"
    android:viewportWidth="109"
    android:viewportHeight="115">
    <path
        android:fillColor="#fbd1b1"
        android:pathData="M74.25,86.69C74.67,78.44 98.97,71.97 101.5,72.5C102.49,72.71 109.02,73.29 102.33,79.76C97.84,84.1 89.43,90.29 86.87,91.78C80.47,95.52 73.82,94.94 74.25,86.69L74.25,86.69Z"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.1603"/>
    <path
        android:fillColor="#f9c731"
        android:pathData="M28.3,112.63C38,92.39 36.69,82.29 37.37,78.36C38.81,70.08 54.8,81.76 55.39,81.82C61.86,82.55 63.75,83.09 63.95,83.43L63.96,83.46L64.09,83.44C65.23,83.29 66.96,83.1 69.53,82.87C73.71,82.49 80.17,79.36 83.71,86.68C84.58,88.47 87.45,105.27 90,110.81L90.28,111.43C90.6,112.12 90.92,112.81 91.24,113.49M63.96,83.45L63.86,83.47C59.96,84.01 64.09,83.99 63.96,83.46V83.45Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M79.89,81.21L91.35,112.51"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.1603"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M37.37,84.54L29.48,112.15"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.1603"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"/>
    <path
        android:fillColor="#b27b48"
        android:pathData="M26.89,4.78C19.37,6.08 13.09,10.29 9.02,16.05C4.96,21.81 3.09,29.11 4.41,36.6C5.49,42.75 8.54,48.07 12.81,52.02C13.32,60.98 15.59,69.76 19.44,76.08C23.42,82.62 29.23,86.21 35.16,88.06C39.2,89.32 43.3,89.77 46.91,89.81C49.45,89.83 51.75,89.65 53.61,89.4C54.94,89.23 56.04,89.02 56.85,88.83C57.39,88.7 57.81,88.58 58.09,88.48C58.32,88.4 58.48,88.32 58.57,88.25L59.86,87.43L58.57,86.6C57.57,85.95 56.67,85.3 55.86,84.62C60.29,84.62 64.92,84.11 69.5,83.31C79.92,81.51 91.27,78.98 98.3,73.17C101.08,70.87 103.2,68.06 104.36,64.61C105.41,61.5 105.68,57.85 104.93,53.55C104.55,51.38 103.82,49.21 102.79,47.07C103.26,44.15 102.66,40.94 101.23,37.68C103.84,33.15 104.94,27.73 103.97,22.18C102.86,15.88 99.3,10.62 94.43,7.21C89.56,3.8 83.38,2.24 77.05,3.34C72.1,4.2 67.79,6.55 64.51,9.86C63.28,9.39 61.96,8.99 60.58,8.65C58.22,8.07 52.5,7.54 45.85,8.3C40.35,5.01 33.69,3.6 26.89,4.78L26.89,4.78Z"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.9924"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M84.99,48.03C97.11,45.94 105.21,34.46 103.1,22.4C100.98,10.35 89.45,2.27 77.33,4.36C65.22,6.46 57.11,17.93 59.22,29.99C61.34,42.05 72.88,50.12 84.99,48.03Z"/>
    <path
        android:fillColor="#fbd1b1"
        android:pathData="M69.44,82.39C51.74,85.45 33.22,84.48 29.62,63.99C26.03,43.51 28.75,18.88 46.44,15.82C64.13,12.76 100.46,33.28 104.06,53.77C107.65,74.26 87.13,79.33 69.43,82.39H69.44Z"/>
    <path
        android:fillColor="#ff82a1"
        android:pathData="M104.06,53.77C103.93,53.06 103.76,52.35 103.56,51.63C103.44,51.65 103.32,51.67 103.2,51.69C100.13,52.22 97.83,53.71 98.06,55.02C98.29,56.32 100.96,56.95 104.03,56.42C104.15,56.4 104.27,56.37 104.39,56.35C104.32,55.52 104.21,54.66 104.06,53.77L104.06,53.77Z"/>
    <path
        android:fillColor="#b27b48"
        android:pathData="M69.98,43.38C64.36,50.12 55,54.42 51.46,62.8C50.47,65.15 44.19,79.13 58.18,87.4C58.94,87.85 31.84,94.43 20.4,75.62C11.81,61.51 11.32,34.89 20.61,22.13C31.38,7.34 55.02,8.32 60.45,9.65C77.52,13.86 82.93,27.85 69.99,43.38H69.98Z"/>
    <path
        android:fillColor="#b27b48"
        android:pathData="M65.19,13.91C80.4,12.42 104.51,33.46 101.86,47.27C98.19,39.82 90.82,34.09 79.73,30.07C68.64,26.05 63.79,20.66 65.19,13.91V13.91Z"/>
    <path
        android:fillColor="#f8c236"
        android:pathData="M36.34,58.08C50.84,55.57 60.55,41.84 58.01,27.4C55.48,12.97 41.67,3.3 27.17,5.81C12.67,8.31 2.96,22.05 5.5,36.48C8.03,50.92 21.84,60.59 36.34,58.08Z"/>
    <path
        android:fillColor="#ff82a1"
        android:pathData="M51.89,70.03C56.61,69.22 60.15,66.93 59.8,64.91C59.44,62.9 55.33,61.94 50.61,62.75C45.89,63.57 42.35,65.86 42.7,67.87C43.05,69.88 47.17,70.85 51.89,70.03Z"/>
    <path
        android:fillColor="#ff82a1"
        android:pathData="M80.77,67.08C80.77,67.08 82.98,76.64 86.56,75.75C89.18,75.1 89.27,73.17 90.02,64.42"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M80.77,67.08C80.77,67.08 82.98,76.64 86.56,75.75C89.18,75.1 89.27,73.17 90.02,64.42"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.1603"/>
    <path
        android:fillColor="#b27b48"
        android:pathData="M90.32,66.55C88.72,67.11 87.02,66.91 85.64,66.13C85.07,67.59 83.87,68.81 82.26,69.38C79.33,70.41 76.09,68.86 75.06,65.94C74.8,65.23 75.18,64.45 75.89,64.2C76.61,63.95 77.39,64.32 77.65,65.03C78.18,66.53 79.84,67.32 81.35,66.8C82.86,66.27 83.65,64.62 83.12,63.12C82.87,62.4 83.24,61.62 83.96,61.37C84.67,61.12 85.46,61.5 85.71,62.21C86.24,63.71 87.9,64.5 89.41,63.97C90.92,63.44 91.72,61.79 91.18,60.29C90.93,59.58 91.3,58.8 92.02,58.55C92.74,58.3 93.52,58.68 93.78,59.39C94.82,62.31 93.27,65.52 90.33,66.55H90.32Z"/>
    <path
        android:fillColor="#fbd1b1"
        android:pathData="M45.74,93.05C51.74,101.33 81,91.72 83.24,89.47C84.11,88.59 90.42,83.6 79.2,81.37C71.67,79.88 58.88,79.11 55.25,79.28C46.17,79.72 39.73,84.77 45.74,93.05Z"
        android:strokeColor="#b27b48"
        android:strokeWidth="4.1603"/>
    <path
        android:fillColor="#ffffff"
        android:pathData="M43.44,22.47C44.62,24.59 45.41,26.35 45.9,27.81C46.79,30.45 47.16,31.98 47.02,32.35C45.7,33.11 44.81,33.39 44.22,33.2C42.3,28.48 40.98,25.51 40.22,24.2C39.59,23.11 38.81,22.27 38,21.69C36.88,21.17 35.85,21.11 34.99,21.55C33.6,22.4 32.34,25.16 31.19,29.84C26.94,26.57 23.82,25.44 21.91,26.48C20.59,27.24 19.7,28.74 19.24,30.98C19.06,30.97 18.64,30.75 18,30.36C17.38,30.01 17,29.65 16.82,29.34C17.12,26.43 18.23,24.4 20.23,23.2C22.67,21.85 25.78,22.46 29.51,25.04C30.03,21.71 31.22,19.51 33.13,18.36C35.11,17.22 37.2,17.19 39.41,18.26C40.96,19.11 42.29,20.5 43.44,22.47H43.44ZM24.98,44.58C24.79,45.04 24.44,45.36 23.91,45.66C23.21,46.06 22.7,46.3 22.52,46.4C21.34,45.27 19.31,42.48 16.44,37.95C14.48,34.88 12.91,32.28 11.67,30.15C11.41,29.71 11.18,29.32 11,28.9C11.34,28.47 11.73,28.13 12.08,27.93C12.78,27.52 13.29,27.29 13.65,27.2C14.83,28.62 16.63,31.2 19.08,34.92C21.35,38.52 23.08,41.3 24.23,43.27L24.99,44.57L24.98,44.58Z"
        android:strokeColor="#ffffff"
        android:strokeWidth="0.5991"/>
</vector>
