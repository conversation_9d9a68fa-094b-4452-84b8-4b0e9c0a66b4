<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/scene_anime_intro">
    <androidx.constraintlayout.widget.Guideline
        android:orientation="horizontal"
        android:id="@+id/scroll_guideline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_begin="65dp"/>
    <include
        android:id="@+id/intro_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/view_animate_intro_menu"/>
    <ImageView
        android:id="@+id/bg_tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:src="@drawable/bg_intro_bar"
        android:scaleType="fitXY"
        app:layout_constraintTop_toBottomOf="@+id/intro_menu"/>
    <View
        android:id="@+id/bg_intro"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bg_tabLayout"/>
    <TextView
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/brown_124"
        android:ellipsize="end"
        android:id="@+id/intro_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:maxLines="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bg_tabLayout"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/favorite_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toStartOf="@+id/line_divider"
        app:layout_constraintStart_toStartOf="@+id/intro_title"
        app:layout_constraintTop_toBottomOf="@+id/intro_title"/>
    <View
        android:id="@+id/line_divider"
        android:background="@drawable/line_divider"
        android:layout_width="4dp"
        android:layout_height="12dp"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/favorite_count"
        app:layout_constraintStart_toEndOf="@+id/favorite_count"
        app:layout_constraintTop_toTopOf="@+id/favorite_count"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/view_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toEndOf="@+id/line_divider"
        app:layout_constraintTop_toTopOf="@+id/favorite_count"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/line_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:minWidth="0dp"
        android:text="@string/line_btn"
        android:backgroundTint="@color/grey_720"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_line_brown"
        app:iconSize="25dp"
        app:iconTint="@null"
        app:layout_constraintStart_toStartOf="@+id/intro_title"
        app:layout_constraintTop_toBottomOf="@+id/favorite_count"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/quality_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="0dp"
        android:text="@string/quality_sd"
        android:layout_marginStart="10dp"
        android:backgroundTint="@color/grey_720"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_sd_brown"
        app:iconSize="25dp"
        app:iconTint="@null"
        app:layout_constraintStart_toEndOf="@+id/line_btn"
        app:layout_constraintTop_toTopOf="@+id/line_btn"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/download_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="0dp"
        android:text="@string/download_state_start"
        android:layout_marginStart="10dp"
        android:backgroundTint="@color/grey_720"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_download_brown"
        app:iconSize="25dp"
        app:iconTint="@null"
        app:layout_constraintStart_toEndOf="@+id/quality_btn"
        app:layout_constraintTop_toTopOf="@+id/line_btn"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <include
        android:id="@+id/intro_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/line_btn"
        layout="@layout/carousel_ad_container"/>
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/intro_ad"
        style="@style/IntroTabLayout"/>
    <com.mimei17.view.NestedScrollableHost
        android:id="@+id/nested_host"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout">
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/intro_viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"/>
    </com.mimei17.view.NestedScrollableHost>
</androidx.constraintlayout.motion.widget.MotionLayout>
