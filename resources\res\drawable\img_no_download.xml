<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="136dp"
    android:width="136dp"
    android:viewportWidth="136"
    android:viewportHeight="136">
    <group>
        <clip-path android:pathData="M0,0h136v136h-136z"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M120.81,112.69C128.11,100.06 123.78,83.9 111.14,76.6C98.5,69.3 82.34,73.63 75.05,86.27C67.75,98.91 72.08,115.07 84.72,122.37C97.36,129.66 113.52,125.33 120.81,112.69Z"/>
        <path
            android:fillColor="#ffeda4"
            android:pathData="M127.81,78.93C136.47,63.93 131.33,44.74 116.33,36.08C101.32,27.42 82.14,32.56 73.48,47.56C64.82,62.56 69.96,81.75 84.96,90.41C99.96,99.07 119.15,93.93 127.81,78.93Z"/>
        <path
            android:fillColor="#ffe57a"
            android:pathData="M87.87,111.37C99.17,91.81 92.47,66.8 72.91,55.51C53.35,44.22 28.34,50.92 17.05,70.48C5.75,90.04 12.46,115.05 32.01,126.34C51.57,137.63 76.58,130.93 87.87,111.37Z"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M32.25,55.51C32.25,72.54 29.76,86.34 69.56,86.34C109.36,86.34 106.88,72.54 106.88,55.51C106.88,38.48 90.17,24.68 69.56,24.68C48.95,24.68 32.25,38.48 32.25,55.51Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M63.22,68L57.73,75.84"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M70.93,68L65.45,75.84"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M78.64,68L73.16,75.84"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#ffd00f"
            android:pathData="M108.9,20.03C102.36,13.49 91.76,13.49 85.22,20.03C78.68,26.58 83.02,32.83 89.56,39.37C96.09,45.91 102.35,50.25 108.89,43.71C115.44,37.17 115.44,26.57 108.9,20.03ZM51.54,20.03C44.99,13.49 34.39,13.49 27.85,20.03C21.31,26.57 21.32,37.17 27.86,43.71C34.4,50.25 40.66,45.91 47.2,39.37C53.74,32.83 58.08,26.58 51.54,20.03Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M108.9,20.03L110.1,18.83L110.1,18.83L108.9,20.03ZM85.22,20.03L84.01,18.83L84.01,18.83L85.22,20.03ZM89.56,39.37L88.35,40.58L88.35,40.58L89.56,39.37ZM108.89,43.71L110.1,44.91L110.1,44.91L108.89,43.71ZM51.54,20.03L52.74,18.83L52.74,18.83L51.54,20.03ZM27.85,20.03L26.65,18.83L26.65,18.83L27.85,20.03ZM27.86,43.71L26.66,44.91L26.66,44.91L27.86,43.71ZM47.2,39.37L48.4,40.58L48.4,40.58L47.2,39.37ZM108.9,20.03L107.7,21.23C101.82,15.36 92.3,15.36 86.42,21.24L85.22,20.03L84.01,18.83C91.22,11.63 102.9,11.62 110.1,18.83L108.9,20.03ZM85.22,20.03L86.42,21.24C84.95,22.71 84.18,24.07 83.86,25.32C83.55,26.55 83.64,27.82 84.09,29.16C85.04,31.95 87.49,34.9 90.76,38.17L89.56,39.37L88.35,40.58C85.09,37.31 82.1,33.86 80.87,30.25C80.24,28.39 80.06,26.46 80.57,24.48C81.07,22.51 82.22,20.63 84.01,18.83L85.22,20.03ZM89.56,39.37L90.76,38.17C94.03,41.44 96.98,43.89 99.77,44.84C101.11,45.29 102.38,45.38 103.61,45.07C104.86,44.75 106.22,43.98 107.69,42.51L108.89,43.71L110.1,44.91C108.3,46.71 106.42,47.86 104.45,48.36C102.47,48.87 100.54,48.69 98.68,48.06C95.07,46.83 91.62,43.84 88.35,40.58L89.56,39.37ZM108.89,43.71L107.69,42.51C113.57,36.63 113.57,27.1 107.7,21.23L108.9,20.03L110.1,18.83C117.31,26.03 117.3,37.71 110.1,44.91L108.89,43.71ZM51.54,20.03L50.33,21.24C44.46,15.36 34.93,15.36 29.05,21.23L27.85,20.03L26.65,18.83C33.85,11.62 45.53,11.63 52.74,18.83L51.54,20.03ZM27.85,20.03L29.05,21.23C23.18,27.1 23.18,36.63 29.06,42.51L27.86,43.71L26.66,44.91C19.45,37.71 19.45,26.03 26.65,18.83L27.85,20.03ZM27.86,43.71L29.06,42.51C30.53,43.98 31.89,44.75 33.14,45.07C34.38,45.38 35.64,45.29 36.98,44.84C39.77,43.89 42.72,41.44 45.99,38.17L47.2,39.37L48.4,40.58C45.13,43.84 41.69,46.83 38.07,48.06C36.22,48.69 34.28,48.87 32.3,48.36C30.33,47.86 28.45,46.71 26.66,44.91L27.86,43.71ZM47.2,39.37L45.99,38.17C49.27,34.9 51.72,31.95 52.66,29.16C53.12,27.82 53.21,26.55 52.89,25.32C52.57,24.07 51.81,22.71 50.33,21.24L51.54,20.03L52.74,18.83C54.54,20.63 55.68,22.51 56.18,24.48C56.69,26.46 56.51,28.39 55.88,30.25C54.66,33.86 51.67,37.31 48.4,40.58L47.2,39.37Z"/>
        <path
            android:fillColor="#954a2c"
            android:pathData="M69.5,21.15C62.97,18.7 51.54,16.53 43.58,26.12C34.78,36.74 24.6,52.91 26.25,68.87C28.11,86.86 49.54,84.62 49.12,84.36C40.65,79.15 41.78,68.79 46.71,62.21C49.49,58.51 61.64,48.71 69.5,39.64C77.35,48.71 89.5,58.51 92.28,62.21C97.21,68.79 98.35,79.15 89.87,84.36C89.46,84.62 110.88,86.85 112.74,68.87C114.39,52.91 104.22,36.74 95.41,26.12C87.45,16.53 76.02,18.7 69.5,21.15Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M69.5,21.15L70.09,22.74L69.5,22.97L68.9,22.74L69.5,21.15ZM43.58,26.12L42.27,25.04L42.27,25.04L43.58,26.12ZM26.25,68.87L24.56,69.04L24.56,69.04L26.25,68.87ZM49.12,84.36L50.01,82.91L50.01,82.91L49.12,84.36ZM46.71,62.21L45.35,61.2L45.35,61.2L46.71,62.21ZM69.5,39.64L68.21,38.53L69.5,37.05L70.78,38.53L69.5,39.64ZM92.28,62.21L93.64,61.2L93.64,61.2L92.28,62.21ZM89.87,84.36L88.96,82.92L88.97,82.92L88.98,82.91L89.87,84.36ZM112.74,68.87L114.43,69.04L114.43,69.04L112.74,68.87ZM95.41,26.12L96.72,25.04L96.72,25.04L95.41,26.12ZM69.5,21.15L68.9,22.74C62.51,20.35 52.1,18.51 44.89,27.21L43.58,26.12L42.27,25.04C50.98,14.54 63.43,17.06 70.09,19.56L69.5,21.15ZM43.58,26.12L44.89,27.21C40.54,32.45 35.89,39.02 32.53,46.21C29.17,53.41 27.16,61.13 27.94,68.69L26.25,68.87L24.56,69.04C23.69,60.66 25.94,52.31 29.45,44.77C32.97,37.24 37.81,30.41 42.27,25.04L43.58,26.12ZM26.25,68.87L27.94,68.69C28.37,72.83 29.9,75.7 31.88,77.73C33.89,79.78 36.45,81.06 39.03,81.83C41.61,82.6 44.14,82.85 46.01,82.88C46.95,82.9 47.69,82.86 48.18,82.81C48.42,82.79 48.57,82.77 48.62,82.76C48.68,82.75 48.59,82.76 48.46,82.81C48.42,82.83 48.34,82.86 48.25,82.92C48.18,82.96 47.97,83.08 47.79,83.32C47.58,83.59 47.29,84.15 47.49,84.84C47.66,85.43 48.08,85.72 48.23,85.81L49.12,84.36L50.01,82.91C50.16,83 50.58,83.3 50.76,83.89C50.96,84.59 50.67,85.15 50.46,85.43C50.27,85.67 50.06,85.8 49.97,85.85C49.87,85.91 49.78,85.95 49.73,85.97C49.54,86.04 49.36,86.08 49.28,86.1C49.06,86.14 48.79,86.17 48.49,86.2C47.87,86.26 47,86.3 45.95,86.28C43.87,86.24 41.01,85.97 38.05,85.09C35.1,84.2 31.97,82.68 29.45,80.1C26.91,77.5 25.06,73.9 24.56,69.04L26.25,68.87ZM49.12,84.36L48.23,85.81C43.46,82.88 41.37,78.44 41.19,73.87C41.02,69.36 42.69,64.75 45.35,61.2L46.71,62.21L48.07,63.23C45.81,66.26 44.45,70.12 44.59,73.73C44.73,77.28 46.3,80.63 50.01,82.91L49.12,84.36ZM46.71,62.21L45.35,61.2C46.14,60.14 47.52,58.74 49.16,57.17C50.83,55.58 52.87,53.71 55.04,51.69C59.41,47.64 64.35,42.98 68.21,38.53L69.5,39.64L70.78,40.75C66.78,45.37 61.72,50.14 57.35,54.19C55.16,56.22 53.15,58.06 51.51,59.63C49.85,61.22 48.67,62.44 48.07,63.23L46.71,62.21ZM69.5,39.64L70.78,38.53C74.64,42.98 79.58,47.64 83.95,51.69C86.12,53.71 88.16,55.58 89.83,57.17C91.47,58.74 92.85,60.14 93.64,61.2L92.28,62.21L90.92,63.23C90.32,62.44 89.14,61.22 87.48,59.63C85.84,58.06 83.83,56.22 81.64,54.19C77.28,50.14 72.21,45.37 68.21,40.75L69.5,39.64ZM92.28,62.21L93.64,61.2C96.3,64.75 97.98,69.36 97.8,73.87C97.62,78.44 95.53,82.88 90.76,85.81L89.87,84.36L88.98,82.91C92.69,80.63 94.26,77.28 94.4,73.73C94.54,70.12 93.18,66.26 90.92,63.23L92.28,62.21ZM89.87,84.36L90.78,85.8C90.93,85.7 91.36,85.4 91.52,84.79C91.69,84.09 91.39,83.54 91.19,83.3C91,83.07 90.81,82.95 90.73,82.91C90.64,82.86 90.57,82.83 90.53,82.81C90.4,82.76 90.31,82.75 90.37,82.76C90.42,82.77 90.57,82.79 90.82,82.81C91.3,82.86 92.05,82.9 92.98,82.88C94.85,82.85 97.39,82.6 99.97,81.83C102.55,81.05 105.1,79.78 107.11,77.72C109.09,75.7 110.62,72.82 111.05,68.69L112.74,68.87L114.43,69.04C113.93,73.9 112.08,77.5 109.54,80.1C107.02,82.68 103.9,84.2 100.94,85.08C97.99,85.97 95.13,86.24 93.04,86.28C92,86.3 91.12,86.26 90.51,86.2C90.2,86.17 89.93,86.14 89.72,86.1C89.64,86.08 89.45,86.04 89.27,85.97C89.22,85.95 89.13,85.91 89.03,85.85C88.95,85.81 88.74,85.68 88.55,85.45C88.35,85.2 88.04,84.65 88.22,83.95C88.38,83.33 88.81,83.02 88.96,82.92L89.87,84.36ZM112.74,68.87L111.05,68.69C111.83,61.13 109.82,53.41 106.46,46.21C103.1,39.02 98.45,32.45 94.1,27.21L95.41,26.12L96.72,25.04C101.18,30.41 106.02,37.24 109.54,44.77C113.05,52.31 115.3,60.66 114.43,69.04L112.74,68.87ZM95.41,26.12L94.1,27.21C86.89,18.51 76.48,20.35 70.09,22.74L69.5,21.15L68.9,19.56C75.56,17.06 88.02,14.54 96.72,25.04L95.41,26.12Z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M41.16,66.3L38.28,69.7L45.93,66.3L43.05,69.7"
            android:strokeColor="#ff8400"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M93,66.3L90.13,69.7L97.78,66.3L94.9,69.7"
            android:strokeColor="#ff8400"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M46.75,79.9L89.25,79.9A10.2,10.2 0,0 1,99.45 90.1L99.45,120.7A10.2,10.2 0,0 1,89.25 130.9L46.75,130.9A10.2,10.2 0,0 1,36.55 120.7L36.55,90.1A10.2,10.2 0,0 1,46.75 79.9z"/>
        <path
            android:fillColor="#f6f1e9"
            android:pathData="M68.85,79.9H89.25C94.88,79.9 99.45,84.47 99.45,90.1V119C99.45,124.63 94.88,129.2 89.25,129.2H68.85V79.9Z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M46.75,81.7L89.25,81.7A8.4,8.4 0,0 1,97.65 90.1L97.65,120.7A8.4,8.4 0,0 1,89.25 129.1L46.75,129.1A8.4,8.4 0,0 1,38.35 120.7L38.35,90.1A8.4,8.4 0,0 1,46.75 81.7z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.604"/>
        <path
            android:fillColor="#ff8400"
            android:pathData="M58.65,98.6L58.65,98.6A6.8,6.8 0,0 1,65.45 105.4L65.45,105.4A6.8,6.8 0,0 1,58.65 112.2L58.65,112.2A6.8,6.8 0,0 1,51.85 105.4L51.85,105.4A6.8,6.8 0,0 1,58.65 98.6z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M58.65,95.2V91.8C58.65,89.92 60.17,88.4 62.05,88.4H75.65C77.53,88.4 79.05,89.92 79.05,91.8V117.3C79.05,119.18 77.53,120.7 75.65,120.7H62.05C60.17,120.7 58.65,119.18 58.65,117.3V115.6"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M67.15,93.5H70.55"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M56.95,108.8H60.35"
            android:strokeColor="#ffffff"
            android:strokeWidth="1.7"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M58.65,101.15L58.65,104.55"
            android:strokeColor="#ffffff"
            android:strokeWidth="1.7"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M56.95,104.13L58.39,105.31C58.53,105.43 58.77,105.43 58.91,105.31L60.35,104.13"
            android:strokeColor="#ffffff"
            android:strokeWidth="1.7"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M36.93,74.8C43.68,74.8 49.22,80.14 49.22,86.81C49.22,93.49 43.68,98.83 36.93,98.83C30.19,98.83 24.65,93.49 24.65,86.81C24.65,80.14 30.19,74.8 36.93,74.8Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M100.61,74.8C107.35,74.8 112.89,80.14 112.89,86.81C112.89,93.49 107.35,98.83 100.61,98.83C93.86,98.83 88.32,93.49 88.32,86.81C88.32,80.14 93.86,74.8 100.61,74.8Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"/>
    </group>
</vector>
