<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fiction_item_layout"
    android:background="@color/white"
    android:padding="5dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="15sp"
        android:textColor="@color/black_153"
        android:id="@+id/fiction_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintEnd_toStartOf="@+id/fiction_read_hint"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Theme.Text.OneLine"/>
    <ImageView
        android:id="@+id/fiction_read_hint"
        android:background="@color/white"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_fiction_read"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"
        app:layout_constraintWidth_percent="0.09"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_459"
        android:id="@+id/fiction_item_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        app:layout_constraintBottom_toTopOf="@+id/fiction_item_remark"
        app:layout_constraintEnd_toStartOf="@+id/fiction_read_hint"
        app:layout_constraintStart_toStartOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_bg"
        app:layout_constraintVertical_chainStyle="spread_inside"
        style="@style/Theme.Text.ThreeLine"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/fiction_item_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        app:layout_constraintDimensionRatio="1.9:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_title"
        app:shapeAppearance="@style/ShapeAppearance.fiction.item2"
        app:strokeColor="@color/white_726"
        app:strokeWidth="1dp"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/yellow_496"
        android:gravity="center"
        android:id="@+id/fiction_item_read_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/fiction_rv_item_read_tag"
        android:layout_marginEnd="3dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_remark"
        app:layout_constraintEnd_toStartOf="@+id/fiction_item_remark"/>
    <ImageView
        android:id="@+id/fiction_item_remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp"
        android:src="@drawable/ic_arrow_fiction_read"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_bg"
        app:layout_constraintEnd_toEndOf="@+id/fiction_item_bg"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_desc"/>
</androidx.constraintlayout.widget.ConstraintLayout>
