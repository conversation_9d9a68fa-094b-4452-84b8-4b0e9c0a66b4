<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fiction_item_layout"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/fiction_item_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="175:110"
        app:layout_constraintTop_toBottomOf="@+id/guideline"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.fiction.item1"
        app:strokeColor="@color/grey_720"
        app:strokeWidth="1dp"/>
    <ImageView
        android:id="@+id/fiction_item_tag_new"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="@+id/fiction_item_bg"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_bg"/>
    <TextView
        android:textSize="16sp"
        android:textColor="@color/brown_124"
        android:id="@+id/fiction_item_title"
        android:background="@color/white"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:drawablePadding="10dp"
        app:drawableStartCompat="@drawable/ic_fiction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Theme.Text.OneLine"/>
    <ImageView
        android:id="@+id/fiction_read_hint"
        android:background="@color/white"
        android:paddingTop="30sp"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_fiction_read"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_title"
        app:layout_constraintEnd_toEndOf="@+id/fiction_item_bg"
        app:layout_constraintStart_toEndOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"
        app:layout_constraintWidth_percent="0.09"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_443"
        android:id="@+id/fiction_item_desc"
        android:padding="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:lineSpacingExtra="4dp"
        app:layout_constraintBottom_toTopOf="@+id/fiction_item_like"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_title"
        style="@style/Theme.Text.ThreeLine"/>
    <ImageView
        android:id="@+id/fiction_item_like"
        android:background="@color/white"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_margin="5dp"
        android:src="@drawable/selector_btn_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fiction_item_desc"/>
    <View
        android:id="@+id/guideline"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/fiction_item_title"
        app:layout_constraintTop_toTopOf="@+id/fiction_item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
