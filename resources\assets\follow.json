{"nm": "Comp 1", "ddd": 0, "h": 90, "w": 360, "meta": {"g": "@lottiefiles/toolkit-js 0.66.1"}, "layers": [{"ty": 4, "nm": "Shape Layer 1", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "908", "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Rectangle 1", "it": [{"ty": "rc", "nm": "Rectangle Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 15}, "s": {"a": 0, "k": [359.525, 89.926]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.898, 0.4784]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [-0.237, -0.037]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 1}, {"ty": 0, "nm": "tag_btn", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "502", "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [180, 45]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 360, "h": 90, "refId": "1", "ind": 2, "tp": 1}], "v": "5.7.0", "fr": 60, "op": 166, "ip": 0, "assets": [{"nm": "tag_btn", "id": "1", "layers": [{"ty": 0, "nm": "mi<PERSON>_doll", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "907", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128]}, "s": {"a": 0, "k": [43, 43, 100]}, "p": {"a": 0, "k": [64, 41.5, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 256, "h": 256, "refId": "2", "ind": 1}, {"ty": 4, "nm": "Words_02 Outlines", "sr": 1, "st": 0, "op": 600.601, "ip": 0, "ln": "766", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [180, 45, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [180, 58, 0], "t": 10}, {"s": [180, 45, 0], "t": 32}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 16}, {"s": [100], "t": 32}]}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.848, -4.138], [-0.88, -6.027], [0.882, -6.027], [0.848, -4.138], [0.529, 2.072], [-0.529, 2.072]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.642], [-0.561, 0], [0, -0.639], [0.56, 0]], "o": [[0, -0.639], [0.56, 0], [0, 0.642], [-0.578, 0]], "v": [[-1.025, 4.969], [0.001, 3.897], [1.024, 4.969], [0.001, 6.026]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [239.914, 57.444]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 2", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.48, 5.715], [4.499, 5.715], [4.499, 4.53], [-2.48, 4.53]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.48, 3.698], [4.499, 3.698], [4.499, 2.577], [-2.48, 2.577]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.497, 0.64], [-2.482, 0.64], [-2.482, 1.729], [4.497, 1.729]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[4.497, 1.729]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.304, -0.145], [0, 0], [0, 0], [0, 0], [0, 0], [0.194, -0.4], [0, 0], [0, 0], [0, 0], [0.239, -0.368], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.104, -0.657], [0.257, 0.24], [-1.008, 1.698], [0, 0], [0, 0], [0, 0], [-0.161, 0.4], [0, 0], [0, 0], [0, 0], [-0.11, 0.369], [1.394, 0], [0.112, 0.256], [-2.561, 0.513]], "o": [[-1.616, 0.321], [0, 0], [0, 0], [0, 0], [0, 0], [-0.16, 0.4], [0, 0], [0, 0], [0, 0], [-0.207, 0.385], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.816, 0.914], [-0.159, -0.272], [1.826, -1.04], [0, 0], [0, 0], [0, 0], [0.209, -0.4], [0, 0], [0, 0], [0, 0], [0.129, -0.368], [-1.504, 0.065], [-0.015, -0.272], [3.905, -0.032], [0, 0]], "v": [[6.193, -6.451], [0.064, -5.762], [-0.305, -4.594], [6.339, -4.594], [6.339, -3.633], [-0.656, -3.633], [-1.186, -2.433], [7.268, -2.433], [7.268, -1.441], [-1.682, -1.441], [-2.352, -0.32], [5.698, -0.32], [5.698, 7.315], [4.497, 7.315], [4.497, 6.675], [-2.48, 6.675], [-2.48, 7.315], [-3.633, 7.315], [-3.633, 1.344], [-6.499, 3.714], [-7.268, 2.769], [-3.057, -1.441], [-6.852, -1.441], [-6.852, -2.433], [-2.514, -2.433], [-1.969, -3.633], [-5.682, -3.633], [-5.682, -4.594], [-1.617, -4.594], [-1.266, -5.699], [-5.667, -5.602], [-5.906, -6.531], [5.426, -7.315]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [227.706, 57.388]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 3", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.417, -0.385], [0, 0], [0.624, 0.448]], "o": [[0.64, 0.416], [0, 0], [-0.416, -0.4], [0, 0]], "v": [[3.954, 3.922], [5.795, 5.315], [5.282, 5.843], [3.441, 4.37]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.591, -0.336], [0, 0], [-0.465, 0.432]], "o": [[-0.609, 0.528], [0, 0], [0.593, -0.368], [0, 0]], "v": [[5.875, 2.129], [3.842, 3.537], [3.409, 2.993], [5.347, 1.521]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.4, -0.368], [0, 0], [0.593, 0.417]], "o": [[0.593, 0.401], [0, 0], [-0.383, -0.385], [0, 0]], "v": [[0.288, 1.568], [2.033, 2.913], [1.52, 3.474], [-0.208, 2.065]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.815, 0.544], [0, 0], [0.657, -0.481], [0, 0]], "o": [[0, 0], [-0.672, 0.529], [0, 0], [0.56, -0.32]], "v": [[1.856, 3.874], [2.145, 4.53], [0.176, 6.019], [-0.304, 5.25]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.729, -4.37], [5.586, -4.37], [5.586, -5.938], [-1.729, -5.938]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.352, -0.144], [0.929, 0], [0.128, 0.239], [-0.16, 0], [0, 0.175], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.409, -2.385], [0.288, 0.127], [0, 2.593], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.512], [-0.368, 0.16], [-0.047, -0.256], [0.656, 0.016], [0.176, -0.016], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.065, 2.625], [-0.208, -0.192], [1.552, -2.561], [0, 0], [0, 0]], "v": [[6.739, -6.946], [6.739, -3.377], [-1.729, -3.377], [-1.729, -2.305], [-1.729, -2.224], [7.251, -2.224], [7.251, -1.232], [3.218, -1.232], [3.218, -0.016], [7.011, -0.016], [7.011, 6.147], [6.547, 7.059], [4.641, 7.219], [4.338, 6.371], [5.73, 6.387], [5.971, 6.147], [5.971, 0.912], [3.218, 0.912], [3.218, 7.108], [2.209, 7.108], [2.209, 0.912], [-0.481, 0.912], [-0.481, 7.203], [-1.473, 7.203], [-1.473, -0.016], [2.209, -0.016], [2.209, -1.232], [-1.744, -1.232], [-3.554, 7.123], [-4.529, 6.483], [-2.849, -2.305], [-2.849, -6.946]]}}}, {"ty": "sh", "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.576, -0.48], [0, 0], [0.816, 0.577]], "o": [[0.817, 0.529], [0, 0], [-0.545, -0.513], [0, 0]], "v": [[-5.891, -7.219], [-3.409, -5.442], [-4.161, -4.609], [-6.61, -6.515]]}}}, {"ty": "sh", "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.656, 1.809], [0, 0], [0.688, -1.473], [0, 0]], "o": [[0, 0], [-0.592, 1.681], [0, 0], [0.656, -1.249]], "v": [[-4.834, 1.217], [-3.922, 1.825], [-6.034, 6.979], [-7.074, 6.371]]}}}, {"ty": "sh", "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.607, -0.448], [0, 0], [0.864, 0.528]], "o": [[0.865, 0.496], [0, 0], [-0.576, -0.481], [0, 0]], "v": [[-6.563, -2.881], [-3.922, -1.2], [-4.642, -0.368], [-7.251, -2.161]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [211.786, 57.452]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 4", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.2, -1.232], [0, 0], [1.905, 1.281]], "o": [[1.889, 1.216], [0, 0], [-1.137, -1.232], [0, 0]], "v": [[1.809, -2.881], [7.331, 1.36], [6.355, 2.289], [0.913, -2.113]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.416, -0.56], [0, 0], [0, 0], [0, 0], [2.224, -1.168], [0.256, 0.272], [-1.585, 2.737], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.336, 0.593], [0, 0], [0, 0], [0, 0], [-1.616, 1.856], [-0.193, -0.304], [3.186, -1.568], [0, 0], [0, 0], [0, 0]], "v": [[6.868, -6.787], [6.868, -5.555], [1.729, -5.555], [0.609, -3.81], [0.609, 6.786], [-0.688, 6.786], [-0.688, -2.161], [-6.514, 2.513], [-7.331, 1.456], [0.193, -5.555], [-6.93, -5.555], [-6.93, -6.787]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [195.946, 57.852]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 5", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-1.152, 0.48], [0, 0], [1.698, -0.256], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.472, -2.096], [0.257, 0.128], [0, 2.129], [0, 0]], "o": [[0, 0], [-1.329, 0.496], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.304], [-0.192, -0.223], [1.361, -1.938], [0, 0], [1.793, -0.288]], "v": [[6.074, -6.995], [7.051, -6.115], [2.184, -4.946], [2.184, -1.873], [7.323, -1.873], [7.323, -0.753], [5.546, -0.753], [5.546, 7.362], [4.378, 7.362], [4.378, -0.753], [2.184, -0.753], [2.184, -0.192], [0.504, 7.362], [-0.425, 6.643], [1.064, -0.192], [1.064, -5.747]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.256, -0.687], [0, 0], [0.496, 0.8]], "o": [[0.513, 0.801], [0, 0], [-0.256, -0.705], [0, 0]], "v": [[-1.273, 2.528], [0.072, 5.073], [-0.793, 5.57], [-2.105, 2.961]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.08, -0.593], [0, 0], [0.304, 0.704]], "o": [[0.32, 0.688], [0, 0], [-0.081, -0.592], [0, 0]], "v": [[-5.034, -4.482], [-4.362, -2.289], [-5.386, -2.017], [-6.011, -4.258]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.192, -0.544], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.24, 0.448]], "o": [[0.305, 0.544], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.192, -0.481], [0, 0]], "v": [[-3.402, -7.379], [-2.537, -5.602], [0.024, -5.602], [0.024, -4.594], [-7.051, -4.594], [-7.051, -5.602], [-3.914, -5.602], [-4.602, -7.075]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.304, -0.672], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.192, 0.737]], "o": [[-0.288, 0.784], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.305, -0.736], [0, 0]], "v": [[-0.873, -4.274], [-1.769, -1.953], [0.28, -1.953], [0.28, -0.929], [-2.842, -0.929], [-2.842, 0.736], [0.088, 0.736], [0.088, 1.792], [-2.842, 1.792], [-2.842, 7.379], [-4.01, 7.379], [-4.01, 1.792], [-7.227, 1.792], [-7.227, 0.736], [-4.01, 0.736], [-4.01, -0.929], [-7.291, -0.929], [-7.291, -1.953], [-2.858, -1.953], [-1.993, -4.515]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.608, -0.816], [0.256, 0.128], [-0.352, 1.056]], "o": [[-0.384, 1.169], [-0.176, -0.16], [0.64, -0.769], [0, 0]], "v": [[-4.826, 2.897], [-6.475, 6.13], [-7.323, 5.554], [-5.787, 2.625]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [179.939, 57.228]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 6", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.592, -1.889], [4.641, -1.889], [4.641, -3.49], [0.592, -3.49]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.016, -0.32]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.337], [0, 0]], "v": [[4.641, 0.703], [4.641, -0.929], [0.592, -0.929], [0.592, -0.273], [0.56, 0.703]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0.336], [0, 0], [0, 0]], "o": [[0, 0], [0.032, -0.32], [0, 0], [0, 0], [0, 0]], "v": [[-4.482, 0.703], [-0.689, 0.703], [-0.657, -0.289], [-0.657, -0.929], [-4.482, -0.929]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.482, -1.889], [-0.656, -1.889], [-0.656, -3.49], [-4.482, -3.49]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.768, -0.511], [-0.192, 0.767], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.832, -0.817], [-3.585, -0.095], [0.064, -0.4], [2.209, 1.169], [2.08, -0.449], [0.255, 0.271], [-0.976, 0.56], [0.544, 0.929], [0, 0]], "o": [[0.737, -0.639], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.191, 0.992], [2.065, 0.929], [-0.224, 0.273], [-3.729, -0.144], [-1.072, 0.704], [-0.144, -0.273], [1.904, -0.353], [-0.784, -0.592], [0, 0], [0.528, 0.832]], "v": [[-2.146, 3.824], [-0.849, 1.697], [-5.635, 1.697], [-5.635, -4.499], [-0.657, -4.499], [-0.657, -5.859], [-7.091, -5.859], [-7.091, -6.947], [6.834, -6.947], [6.834, -5.859], [0.592, -5.859], [0.592, -4.499], [5.842, -4.499], [5.842, 1.697], [0.415, 1.697], [-1.041, 4.433], [7.378, 5.697], [6.866, 6.882], [-2.018, 5.201], [-6.659, 6.947], [-7.379, 5.89], [-3.122, 4.513], [-5.123, 2.256], [-4.098, 1.84]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [164.043, 57.741]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 7", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.521, 1.16], [1.872, 1.16], [1.872, 4.986], [5.521, 4.986]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[5.521, 4.986]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.738, 0.024], [6.738, 6.922], [5.521, 6.922], [5.521, 6.154], [1.872, 6.154], [1.872, 7.018], [0.721, 7.018], [0.721, 0.024]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.234, -2.84], [3.153, -2.84], [3.153, -5.882], [-3.234, -5.882]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.385, -1.688], [-4.402, -1.688], [-4.402, -7.018], [4.385, -7.018]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[4.385, -7.019]]}}}, {"ty": "sh", "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.242, 1.16], [-5.587, 1.16], [-5.587, 4.986], [-2.242, 4.986]]}}}, {"ty": "sh", "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[-2.242, 4.986]]}}}, {"ty": "sh", "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.04, 0.024], [-1.04, 6.875], [-2.242, 6.875], [-2.242, 6.154], [-5.587, 6.154], [-5.587, 7.018], [-6.738, 7.018], [-6.738, 0.024]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [147.979, 57.653]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 8", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.336, -0.704], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.815, -0.896], [0.272, 0.192], [-0.801, 2.353]], "o": [[-0.256, 0.736], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.687, 1.298], [-0.193, -0.208], [1.441, -1.504], [0, 0]], "v": [[1.585, -6.89], [0.689, -4.713], [7.411, -4.713], [7.411, -3.562], [2.434, -3.562], [2.434, -1.24], [7.042, -1.24], [7.042, -0.135], [2.434, -0.135], [2.434, 2.298], [7.252, 2.298], [7.252, 3.434], [2.434, 3.434], [2.434, 7.323], [1.217, 7.323], [1.217, -3.562], [0.111, -3.562], [-2.176, -0.201], [-3.105, -1.016], [0.433, -7.194]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.623, -1.089], [0, 0], [0, 0], [0, 0], [0.545, -0.559], [0.225, 0.274], [-0.896, 2.434]], "o": [[-0.449, 1.17], [0, 0], [0, 0], [0, 0], [-0.512, 0.703], [-0.127, -0.271], [1.585, -1.552], [0, 0]], "v": [[-2.272, -6.971], [-3.921, -3.529], [-3.921, 7.306], [-5.122, 7.306], [-5.122, -1.64], [-6.707, 0.264], [-7.411, -0.937], [-3.425, -7.323]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [131.898, 57.332]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 9", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.932, -4.862], [3.303, -4.862], [3.303, 4.609], [5.932, 4.609]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[5.932, 4.61]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.112, -6.878], [8.112, 7.687], [5.932, 7.687], [5.932, 6.644], [3.303, 6.644], [3.303, 7.958], [1.215, 7.958], [1.215, -6.878]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.181, -0.612], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.486, -0.594], [0, 0], [0.883, 1.098], [1.749, -1.28], [0.449, 0.323], [-0.305, 2.108], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.451, -0.667], [0.487, 0.27], [-0.36, 2.051]], "o": [[-0.126, 0.631], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.918, 0.919], [0, 0], [-0.576, -0.936], [-0.631, 1.765], [-0.287, -0.451], [2.339, -1.692], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.36, 0.918], [-0.395, -0.361], [0.972, -1.296], [0, 0]], "v": [[-4.024, -8.013], [-4.475, -6.159], [0.298, -6.159], [0.298, -4.16], [-2.242, -4.16], [-2.242, -1.729], [-2.242, -1.297], [0.73, -1.297], [0.73, 0.737], [-2.385, 0.737], [-2.511, 1.476], [0.892, 5.257], [-0.567, 7.094], [-3.07, 3.691], [-6.492, 8.427], [-8.038, 6.788], [-4.564, 0.737], [-7.77, 0.737], [-7.77, -1.297], [-4.419, -1.297], [-4.419, -1.746], [-4.419, -4.16], [-5.141, -4.16], [-6.365, -1.746], [-8.111, -2.989], [-6.094, -8.426]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [250.346, 37.091]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 10", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.402, 1.117], [5.725, 1.117], [5.725, 0.109], [3.402, 0.109]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[3.402, 0.108]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.402, -1.404], [5.725, -1.404], [5.725, -2.376], [3.402, -2.376]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[3.402, -2.377]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.647, 1.117], [1.568, 1.117], [1.568, 0.109], [-0.647, 0.109]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.566, -2.377], [-0.648, -2.377], [-0.648, -1.405], [1.566, -1.405]]}}}, {"ty": "sh", "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[1.566, -1.404]]}}}, {"ty": "sh", "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [1.188, -0.72], [-0.306, -0.18], [0, 0], [0, 0], [0.557, -0.27], [1.08, 0], [0.216, 0.45], [-0.181, 0], [0, 0.198], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.739, 0.288], [0, 0], [-0.739, -0.306], [-0.45, 0.343], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.881, 0.846], [0.396, 0.198], [0, 0], [0, 0], [0, 0.847], [-0.54, 0.27], [-0.053, -0.469], [0.593, 0.036], [0.217, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.81, -0.378], [0, 0], [0.647, 0.234], [0.502, -0.288], [0, 0], [0, 0], [0, 0]], "v": [[5.96, -7.85], [6.374, -7.958], [7.652, -6.932], [4.43, -4.483], [5.51, -3.925], [7.671, -3.925], [7.671, 3.583], [6.951, 5.132], [4.574, 5.402], [4.07, 3.799], [5.456, 3.817], [5.727, 3.565], [5.727, 2.683], [3.402, 2.683], [3.402, 5.329], [1.566, 5.329], [1.566, 2.683], [-0.647, 2.683], [-0.647, 5.384], [-2.557, 5.384], [-2.557, -3.925], [1.531, -3.925], [-0.882, -4.951], [0.505, -6.104], [2.648, -5.276], [4.124, -6.266], [-2.34, -6.266], [-2.34, -7.85]]}}}, {"ty": "sh", "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.522, -0.738], [0, 0], [0.828, 0.9]], "o": [[0.846, 0.865], [0, 0], [-0.487, -0.757], [0, 0]], "v": [[-6.175, -8.463], [-3.781, -5.744], [-5.419, -4.555], [-7.777, -7.472]]}}}, {"ty": "sh", "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.181, 0.379], [-0.341, 0.36], [-0.666, 0.954], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.954, -1.224], [0, 0], [0, 0], [0, 0], [0.45, -1.116], [-2.845, -0.018], [-2.052, 0.198], [0.109, -0.577], [2.106, 0], [1.387, 1.782], [0.881, -0.576], [0.414, 0.288], [-0.595, 2.466], [0, 0], [0.162, -0.162]], "o": [[0.289, -0.072], [0.325, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.684, 0.99], [0, 0], [0, 0], [0, 0], [-0.218, 1.495], [1.08, 1.693], [1.908, 0], [-0.306, 0.432], [-1.873, 0.125], [-3.258, 0], [-0.649, 1.044], [-0.27, -0.396], [1.657, -0.99], [0, 0], [-0.434, 0], [-0.108, -0.451]], "v": [[-8.589, 0.324], [-7.652, -0.252], [-5.906, -2.539], [-8.227, -2.539], [-8.227, -4.249], [-4.572, -4.249], [-4.339, -4.339], [-3.024, -3.745], [-5.815, 0.126], [-4.501, 0.126], [-4.158, 0.072], [-3.132, 0.45], [-4.158, 4.375], [1.766, 6.302], [8.697, 5.996], [7.995, 7.995], [1.71, 8.157], [-5.005, 6.032], [-7.309, 8.463], [-8.697, 7.022], [-5.274, 1.801], [-6.787, 1.801], [-7.904, 2.071]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [232.913, 37.109]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 11", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.089, -2.16], [4.474, -2.16], [4.474, -3.33], [1.089, -3.33]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.474, 0.63], [4.474, -0.54], [1.089, -0.54], [1.089, 0.035], [1.071, 0.63]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.649, -0.433], [-0.216, 0.576]], "o": [[0.523, 0.63], [0.521, -0.486], [0, 0]], "v": [[-4.331, 2.377], [-2.583, 3.961], [-1.522, 2.377]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.493, 0.63], [-1.198, 0.63], [-1.179, 0.018], [-1.179, -0.54], [-4.493, -0.54]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.493, -2.16], [-1.18, -2.16], [-1.18, -3.33], [-4.493, -3.33]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.683, -0.756], [-3.674, 0.018], [0.126, -0.721], [2.61, 1.151], [2.089, -0.414], [0.468, 0.505], [-1.009, 0.449], [0.54, 0.81], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.234, 0.918], [2.341, 0.757], [-0.397, 0.486], [-3.944, -0.073], [-1.189, 0.703], [-0.252, -0.487], [1.729, -0.269], [-0.685, -0.559], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.179, -5.077], [-1.179, -6.122], [-8.112, -6.122], [-8.112, -8.049], [7.751, -8.049], [7.751, -6.122], [1.089, -6.122], [1.089, -5.077], [6.617, -5.077], [6.617, 2.377], [0.819, 2.377], [-0.513, 4.915], [8.49, 5.726], [7.571, 7.851], [-2.295, 6.356], [-7.175, 8.049], [-8.49, 6.139], [-4.42, 5.078], [-6.275, 3.043], [-4.619, 2.377], [-6.545, 2.377], [-6.545, -5.077]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [215.103, 37.523]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 12", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.044, 3.358], [5.142, 3.358], [5.142, 1.432], [-0.044, 1.432]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.044, -3.214], [4.458, -3.214], [4.458, -4.924], [-0.044, -4.924]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.108, 0.523], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.198, -0.594], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.694, -6.671], [1.18, -8.472], [3.755, -8.165], [3.053, -6.671], [6.545, -6.671], [6.545, -1.449], [-0.044, -1.449], [-0.044, -0.333], [7.247, -0.333], [7.247, 5.105], [-2.151, 5.105], [-2.151, -6.671]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.523, -0.72], [0, 0], [0.828, 0.9]], "o": [[0.846, 0.864], [0, 0], [-0.486, -0.775], [0, 0]], "v": [[-6.202, -8.417], [-3.807, -5.717], [-5.446, -4.51], [-7.804, -7.427]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.198, 0.379], [-0.361, 0.342], [-0.684, 0.954], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.99, -1.206], [0, 0], [0, 0], [0, 0], [0.468, -1.135], [-2.845, -0.018], [-2.052, 0.198], [0.109, -0.577], [2.106, 0], [1.385, 1.764], [0.864, -0.576], [0.415, 0.288], [-0.594, 2.466], [0, 0], [0.162, -0.162]], "o": [[0.306, -0.09], [0.342, -0.324], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.72, 1.008], [0, 0], [0, 0], [0, 0], [-0.233, 1.513], [1.099, 1.656], [1.908, 0], [-0.305, 0.432], [-1.873, 0.125], [-3.24, 0], [-0.631, 1.026], [-0.269, -0.396], [1.639, -0.99], [0, 0], [-0.433, 0], [-0.109, -0.451]], "v": [[-8.615, 0.333], [-7.642, -0.243], [-5.842, -2.53], [-8.254, -2.53], [-8.254, -4.24], [-4.473, -4.24], [-4.24, -4.33], [-2.925, -3.736], [-5.806, 0.135], [-4.492, 0.135], [-4.168, 0.081], [-3.124, 0.459], [-4.168, 4.421], [1.739, 6.311], [8.67, 6.005], [7.968, 8.004], [1.684, 8.166], [-5.013, 6.059], [-7.283, 8.472], [-8.669, 7.031], [-5.266, 1.81], [-6.814, 1.81], [-7.93, 2.08]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [196.94, 37.1]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 13", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.882, -4.322], [2.778, -4.322], [2.778, 4.674], [5.882, 4.674]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0]], "o": [[0, 0]], "v": [[5.882, 4.674]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.083, -5.49], [7.083, 6.883], [5.882, 6.883], [5.882, 5.826], [2.778, 5.826], [2.778, 7.011], [1.624, 7.011], [1.624, -5.49]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.496, -0.688], [0.465, -0.064], [0.72, 0.048], [0.208, 0.336], [-0.289, 0], [-0.145, 0.208], [-0.128, 6.163], [0, 0], [2.161, -2.353], [0.305, 0.191], [-0.111, 4.034], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.128, 7.123], [-0.288, 0.417], [-0.448, 0.064], [-0.032, -0.353], [0.752, 0.064], [0.239, 0.016], [0.335, -0.464], [0, 0], [-0.144, 4.338], [-0.192, -0.272], [2.017, -2.113], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.225, -7.267], [-3.257, -4.434], [0.312, -4.434], [0.312, -3.826], [-0.424, 6.322], [-1.481, 6.947], [-3.385, 6.947], [-3.737, 5.746], [-2.008, 5.81], [-1.464, 5.554], [-0.856, -3.266], [-3.289, -3.266], [-6.154, 7.267], [-7.083, 6.435], [-4.458, -3.266], [-6.683, -3.266], [-6.683, -4.434], [-4.425, -4.434], [-4.41, -7.267]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [179.442, 37.981]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 14", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.416, -1.105], [0, 0], [0.913, 1.216]], "o": [[0.944, 1.216], [0, 0], [-0.384, -1.104], [0, 0]], "v": [[5.13, 1.064], [7.451, 5.002], [6.442, 5.514], [4.185, 1.513]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.161, -0.912], [0, 0], [0.465, 1.073]], "o": [[0.528, 1.056], [0, 0], [-0.128, -0.945], [0, 0]], "v": [[3.113, 1.64], [4.25, 5.002], [3.273, 5.37], [2.216, 1.944]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.432, -0.175], [1.073, 0], [0.144, 0.336], [-0.208, 0], [0, 0.192], [0, 0]], "o": [[0, 0], [0, 0.624], [-0.432, 0.176], [-0.048, -0.32], [0.801, 0.032], [0.224, 0], [0, 0], [0, 0]], "v": [[1.576, -0.345], [1.576, 5.979], [1.016, 7.098], [-1.193, 7.29], [-1.529, 6.202], [0.168, 6.218], [0.456, 5.962], [0.456, -0.345]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.04, -0.928], [0, 0], [-0.368, 1.217]], "o": [[-0.399, 1.282], [0, 0], [0.993, -0.816], [0, 0]], "v": [[-0.617, 1.752], [-2.713, 5.465], [-3.594, 4.809], [-1.561, 1.384]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.273, -0.689], [0, 0], [0, 0], [0, 0], [-1.329, -0.561], [0.177, -0.288], [0.896, 1.601], [0, 0], [1.825, -0.944], [0.224, 0.225], [-0.8, 1.297], [0, 0], [0, 0], [0, 0], [-0.176, 0.737], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.193, 0.737], [0, 0], [0, 0], [0, 0], [0.848, 1.233], [-0.24, 0.224], [-1.536, -0.736], [0, 0], [-0.896, 1.681], [-0.159, -0.256], [1.489, -0.737], [0, 0], [0, 0], [0, 0], [0.304, -0.689], [0, 0], [0, 0], [0, 0]], "v": [[6.651, -6.522], [6.651, -5.403], [1.961, -5.403], [1.272, -3.257], [7.194, -3.257], [7.194, -2.122], [3.993, -2.122], [7.467, 0.76], [6.682, 1.688], [2.745, -2.122], [0.728, -2.122], [-3.274, 1.944], [-4.01, 1.032], [-0.617, -2.122], [-3.578, -2.122], [-3.578, -3.257], [-0.024, -3.257], [0.696, -5.403], [-2.842, -5.403], [-2.842, -6.522]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.56, -0.528], [0, 0], [0.928, 0.464]], "o": [[0.912, 0.432], [0, 0], [-0.528, -0.545], [0, 0]], "v": [[-6.011, -7.291], [-3.418, -5.643], [-4.138, -4.666], [-6.715, -6.427]]}}}, {"ty": "sh", "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.671, 1.729], [0, 0], [0.705, -1.424], [0, 0]], "o": [[0, 0], [-0.608, 1.601], [0, 0], [0.64, -1.184]], "v": [[-4.891, 1.513], [-3.93, 2.184], [-6.043, 7.083], [-7.115, 6.41]]}}}, {"ty": "sh", "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.576, -0.512], [0, 0], [0.96, 0.416]], "o": [[0.976, 0.384], [0, 0], [-0.56, -0.513], [0, 0]], "v": [[-6.811, -2.97], [-4.106, -1.449], [-4.794, -0.472], [-7.467, -2.089]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [163.987, 37.941]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 15", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.386, 1.256], [-4.386, 5.275], [-0.593, 5.275], [-0.593, 0.025], [-6.979, 0.025], [-6.979, -1.176], [-0.593, -1.176], [-0.593, -3.689], [-5.794, -3.689], [-5.794, -4.889], [-0.593, -4.889], [-0.593, -7.354], [0.656, -7.354], [0.656, -4.889], [5.874, -4.889], [5.874, -3.689], [0.656, -3.689], [0.656, -1.176], [6.979, -1.176], [6.979, 0.025], [0.656, 0.025], [0.656, 5.275], [4.385, 5.275], [4.385, 1.256], [5.618, 1.256], [5.618, 7.354], [4.385, 7.354], [4.385, 6.443], [-5.65, 6.443], [-5.65, 1.256]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [147.93, 37.877]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 16", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.4, -0.881], [0, 0], [0.8, 1.008]], "o": [[0.8, 1.008], [0, 0], [-0.368, -0.88], [0, 0]], "v": [[5.146, 3.513], [7.275, 6.763], [6.139, 7.242], [4.074, 3.93]]}}}, {"ty": "sh", "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.192, -0.8], [0, 0], [0.464, 0.993]], "o": [[0.497, 0.977], [0, 0], [-0.177, -0.801], [0, 0]], "v": [[1.896, 3.817], [3.097, 6.89], [1.929, 7.195], [0.808, 4.057]]}}}, {"ty": "sh", "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.015, -0.769], [0, 0], [0.208, 1.041]], "o": [[0.241, 1.025], [0, 0], [0, -0.8], [0, 0]], "v": [[-1.369, 3.945], [-0.953, 7.067], [-2.169, 7.226], [-2.505, 4.041]]}}}, {"ty": "sh", "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.154, 1.513], [4.218, 1.513], [4.218, -1.352], [-4.154, -1.352]]}}}, {"ty": "sh", "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.665, -2.489], [-0.665, -7.355], [0.536, -7.355], [0.536, -5.658], [6.619, -5.658], [6.619, -4.522], [0.536, -4.522], [0.536, -2.489], [5.418, -2.489], [5.418, 2.633], [-5.29, 2.633], [-5.29, -2.489]]}}}, {"ty": "sh", "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.881, -0.849], [0, 0], [-0.496, 1.185]], "o": [[-0.512, 1.249], [0, 0], [0.848, -0.736], [0, 0]], "v": [[-3.978, 3.913], [-6.187, 7.355], [-7.275, 6.826], [-5.114, 3.609]]}}}, {"ty": "mm", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [131.858, 37.861]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2}, {"ty": 4, "nm": "dot Outlines", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "83", "hasMask": true, "ao": 0, "ks": {"a": {"a": 0, "k": [359.5, 0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [359.5, 0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "masksProperties": [{"nm": "Mask 1", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.207, -14.086], [-9.332, -10.318], [-10.85, -0.068], [8.65, 8.789]], "o": [[0.207, 14.085], [9.332, 10.318], [10.85, 0.068], [-8.65, -8.79]], "v": [[341.543, -31.21], [355.543, 1.932], [390.9, 18.932], [402.4, -40.71]]}], "t": 18}, {"s": [{"c": true, "i": [[-0.207, -14.086], [-9.332, -10.318], [-10.85, -0.068], [8.65, 8.789]], "o": [[0.207, 14.085], [9.332, 10.318], [10.85, 0.068], [-8.65, -8.79]], "v": [[306.793, 4.04], [320.793, 37.182], [356.15, 54.182], [367.65, -5.46]]}], "t": 29}]}}], "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 2", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.63]], "o": [[0.623, 0], [0, -0.63], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.143], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 3", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 4", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 5", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 6", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.143], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 7", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 8", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 9", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.637], [0.628, 0], [0, 0.638]], "o": [[0.618, 0], [0, 0.638], [-0.629, 0], [0, -0.637]], "v": [[0, -1.143], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 32.535]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 10", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 11", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 12", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 53.038]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 13", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 14", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 15", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 16", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 17", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 18", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 19", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 20", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 21", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 22", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 23", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 24", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 25", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 26", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.63]], "o": [[0.622, 0], [0, -0.63], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.143], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 27", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 28", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 29", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.631]], "o": [[0.622, 0], [0, -0.631], [-0.622, 0], [0, 0.632]], "v": [[0, 1.143], [1.128, -0.001], [0, -1.144], [-1.128, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 30", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.104, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 31", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 32", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 33", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.637], [0.628, 0], [0, 0.638]], "o": [[0.618, 0], [0, 0.638], [-0.629, 0], [0, -0.637]], "v": [[0, -1.143], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 32.535]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 34", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 35", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.143], [1.128, 0.001], [0, 1.144], [-1.128, 0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 36", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.143], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 53.038]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 37", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.101, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.654, 18.857]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 38", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.656, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 39", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.101], [0.101, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.102], [-0.1, 0.624], [0.615, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.648, 32.52]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 40", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.101, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.66, 39.358]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 41", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.614, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.101], [-0.1, 0.624], [0.616, 0.101]], "v": [[1.112, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.182, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.653, 46.194]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 42", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.101, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.645, 53.03]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 43", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 44", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.63]], "o": [[0.623, 0], [0, -0.63], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.143], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 25.694]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 45", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 32.525]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 46", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 39.366]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 47", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.631]], "o": [[0.623, 0], [0, -0.631], [-0.623, 0], [0, 0.632]], "v": [[0, 1.143], [1.127, -0.001], [0, -1.144], [-1.127, -0.001]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 46.197]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 48", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.143], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 53.028]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 49", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 50", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 51", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [355.022, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 52", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 53", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 12.031]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 54", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [348.298, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 55", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 56", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 57", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [341.563, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 58", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 59", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 60", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [334.839, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 61", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 62", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 63", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.622, 0], [0, 0.632], [0.622, 0], [0, -0.632]], "o": [[0.622, 0], [0, -0.632], [-0.622, 0], [0, 0.632]], "v": [[0, 1.144], [1.128, 0], [0, -1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [328.105, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 64", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 65", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 12.031]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 66", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.629, 0], [0, -0.638], [0.628, 0], [0, 0.637]], "o": [[0.618, 0], [0, 0.637], [-0.629, 0], [0, -0.638]], "v": [[0, -1.144], [1.128, 0], [0, 1.144], [-1.128, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [321.38, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 67", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.614, 0.102], [0.1, -0.623], [-0.614, -0.102]], "o": [[0.1, -0.623], [-0.615, -0.101], [-0.1, 0.624], [0.615, 0.101]], "v": [[1.113, 0.184], [0.182, -1.129], [-1.112, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.649, 5.183]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 68", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.101], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.102], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.128], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.652, 12.02]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 69", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.1, 0.623], [0.615, 0.102], [0.1, -0.623], [-0.615, -0.102]], "o": [[0.1, -0.623], [-0.614, -0.101], [-0.1, 0.624], [0.614, 0.101]], "v": [[1.113, 0.184], [0.181, -1.129], [-1.113, -0.184], [-0.181, 1.129]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [314.654, 18.858]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 70", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 5.19]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 71", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 12.022]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "nm": "Group 72", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.623, 0], [0, 0.632], [0.623, 0], [0, -0.632]], "o": [[0.623, 0], [0, -0.632], [-0.623, 0], [0, 0.632]], "v": [[0, 1.144], [1.127, 0], [0, -1.144], [-1.127, 0]]}}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [307.921, 18.863]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3}, {"ty": 4, "nm": "Small_circle", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "95", "ddd": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129, -19, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [255.939, -23.516, 0], "t": 0}, {"s": [309, 26, 0], "t": 29}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "or": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [225.91, 229.057, 180], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [37.335, 132.364, 299.63], "t": 14}, {"s": [0, 0, 0], "t": 29}]}}, "shapes": [{"ty": "gr", "nm": "Ellipse 1", "it": [{"ty": "el", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [34.697, 34.697]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.908, -18.987]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50}}]}], "ind": 4}, {"ty": 4, "nm": "Small_circle 2", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "98", "ddd": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129, -19, 0]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [354, 354, 354], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 29}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [243.013, 124.785, 0], "t": 0}, {"s": [315.5, 87.5, 0], "t": 29}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "rx": {"a": 0, "k": 0}, "ry": {"a": 0, "k": 0}, "rz": {"a": 0, "k": 0}, "or": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [225.91, 229.057, 180], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [37.335, 132.364, 299.63], "t": 14}, {"s": [0, 0, 0], "t": 29}]}}, "shapes": [{"ty": "gr", "nm": "Ellipse 1", "it": [{"ty": "el", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [34.697, 34.697]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.5176, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.908, -18.987]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 25}}]}], "ind": 5}, {"ty": 4, "nm": "BG_face", "sr": 1, "st": 0, "op": 14401, "ip": 0, "ln": "100", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [180, 45]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Rectangle 1", "it": [{"ty": "rc", "nm": "Rectangle Path 1", "d": 1, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [364.008, 93.93]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.9843, 0.9451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0.004, -0.035]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6}]}, {"nm": "mi<PERSON>_doll", "id": "2", "layers": [{"ty": 4, "nm": "Head_obj_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "905", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [75.227, 57.274, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [75.227, 57.274, 0], "t": 45.517}, {"s": [87.227, 57.274, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-1.829, -0.979], [-8.449, 10.198], [-5.436, 1.987], [0.135, 0.136], [12.301, -12.314], [-12.32, -12.307]], "o": [[6.493, -14.202], [4.646, -5.608], [-0.13, -0.136], [-12.32, -12.307], [-12.302, 12.314], [1.85, 1.848]], "v": [[-10.782, 30.553], [13.275, -6.774], [28.634, -17.84], [28.249, -18.246], [-16.332, -18.233], [-16.298, 26.348]]}], "t": 45.517}, {"s": [{"c": true, "i": [[-1.829, -0.979], [-8.449, 10.198], [5.07, 5.626], [0.135, 0.136], [12.301, -12.314], [-12.32, -12.307]], "o": [[13.582, 6.147], [4.646, -5.608], [-0.13, -0.136], [-12.32, -12.307], [-12.302, 12.314], [1.85, 1.848]], "v": [[-10.782, 30.553], [28.632, 4.601], [28.634, -17.84], [28.249, -18.246], [-16.332, -18.233], [-16.298, 26.348]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [68.296, 53.247]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 1, "parent": 8}, {"ty": 4, "nm": "shine line_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "904", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [113.311, 133.985, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113.311, 133.985, 0], "t": 45.517}, {"s": [121.54, 133.985, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.157, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [113.424, 132.662]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2, "parent": 7}, {"ty": 4, "nm": "shine line_M Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "903", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [129.088, 133.44, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [129.088, 133.44, 0], "t": 45.517}, {"s": [137.318, 133.44, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.157, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [127.945, 132.654]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3, "parent": 7}, {"ty": 4, "nm": "shine line_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "902", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [142.689, 132.896, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [142.689, 132.896, 0], "t": 45.517}, {"s": [150.919, 132.896, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.156, -7.383], [-5.157, 7.383]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.7294, 0.4431]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [142.463, 132.646]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4, "parent": 7}, {"ty": 4, "nm": "Faceobj_L Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "901", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [79.58, 125.28, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [136, 100, 100], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [79.58, 125.28, 0], "t": 45.517}, {"s": [88.58, 125.28, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.786, -3.199], [-7.199, 3.204], [7.199, -3.204], [1.786, 3.199]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.5176, 0]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [78.837, 125.299]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 5, "parent": 7}, {"ty": 4, "nm": "Faceobj_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "900", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [177.508, 126.912, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [177.508, 126.912, 0], "t": 45.517}, {"s": [185.508, 126.912, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.786, -3.199], [-7.199, 3.204], [7.199, -3.204], [1.786, 3.199]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [1, 0.5176, 0]}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [176.437, 125.247]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6, "parent": 7}, {"ty": 4, "nm": "Face Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "899", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [81, 100, 100], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 45.517}, {"s": [151.337, 127.912, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.063, 0.039], [9.283, 12.379], [7.002, 6.467], [8.248, 9.509], [8.804, -8.148], [2.311, -3.089], [-15.953, -9.804], [0.312, -0.056], [-16.545, 0.009], [-9.674, 2.309]], "o": [[15.942, -9.821], [-2.314, -3.087], [-8.813, -8.138], [-8.238, 9.518], [-6.995, 6.474], [-9.269, 12.39], [0.055, 0.034], [9.69, 2.314], [16.486, -0.009], [-0.419, -0.065]], "v": [[38.366, 40.198], [42.87, -1.49], [27.779, -16.377], [-0.039, -43.958], [-27.828, -16.347], [-42.904, -1.445], [-38.355, 40.239], [-38.759, 40.381], [0.134, 43.949], [38.921, 40.365]]}], "t": 45.517}, {"s": [{"c": true, "i": [[-0.063, 0.039], [9.283, 12.379], [7.002, 6.467], [8.248, 9.509], [8.804, -8.148], [2.311, -3.089], [-15.953, -9.804], [0.312, -0.056], [-16.545, 0.009], [-9.674, 2.309]], "o": [[15.942, -9.821], [-2.314, -3.087], [-8.813, -8.138], [-8.238, 9.518], [-6.995, 6.474], [-9.269, 12.39], [0.055, 0.034], [9.69, 2.314], [16.486, -0.009], [-0.419, -0.065]], "v": [[38.366, 40.198], [46.509, 4.406], [31.418, -11.071], [-0.039, -43.958], [-27.828, -16.347], [-42.904, -1.445], [-38.355, 40.239], [-38.759, 40.381], [0.443, 42.949], [38.921, 40.365]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [130.41, 115.849]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 7, "parent": 8}, {"ty": 4, "nm": "Hair Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "898", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 113, 0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 127, 0], "t": 18.621}, {"s": [128, 128, 0], "t": 33.103}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[16.581, 19.969], [12.281, -4.617], [14.973, -18.072], [-3.125, -30.025], [-4.204, 0.751], [-16.545, 0.009], [-9.674, 2.309], [-3.211, 31.17]], "o": [[-14.992, -18.056], [-12.286, -4.603], [-16.56, 19.987], [3.282, 31.523], [9.69, 2.315], [16.486, -0.009], [4.842, 0.75], [3.093, -30.029]], "v": [[48.759, -47.665], [-0.032, -56.996], [-48.813, -47.613], [-81.39, 32.861], [-38.733, 62.144], [0.16, 65.712], [38.947, 62.128], [81.422, 32.774]]}], "t": 45.517}, {"s": [{"c": true, "i": [[16.362, 20.149], [12.281, -4.617], [17.635, -21.046], [-3.125, -30.025], [-4.204, 0.751], [-16.545, 0.009], [-11.307, 1.749], [-3.211, 31.17]], "o": [[-11.316, -13.935], [-12.286, -4.603], [-16.671, 19.895], [3.282, 31.523], [11.084, 0.557], [16.486, -0.01], [4.842, 0.75], [3.093, -30.029]], "v": [[51.504, -46.881], [12.908, -56.604], [-48.813, -47.613], [-81.39, 32.861], [-12.069, 62.928], [14.668, 64.144], [41.3, 62.912], [81.422, 32.774]]}], "t": 66.206}]}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [0.5843, 0.2902, 0.1725]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [130.383, 94.086]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 8, "parent": 10}, {"ty": 4, "nm": "Head_obj_R Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "897", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [168.259, 71.963, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100, 100, 100], "t": 45.517}, {"s": [93, 93, 98.936], "t": 66.206}]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [168.259, 71.963, 0], "t": 45.517}, {"s": [165.342, 76.036, 0], "t": 66.206}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-12.314, -12.3], [-12.307, 12.319], [12.314, 12.3], [12.307, -12.321]], "o": [[12.314, 12.301], [12.307, -12.321], [-12.314, -12.301], [-12.307, 12.319]], "v": [[-14.11, 14.132], [22.297, 22.282], [22.284, -22.3], [-22.297, -22.265]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [182.24, 57.233]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 9, "parent": 8}, {"ty": 4, "nm": "Body Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "896", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [128, 128, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 360, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [128, 128, 0], "t": 10.345}, {"s": [128, 141, 0], "t": 20.689}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [-19.678, 0], [0, 0], [0, -20.376], [0, 0]], "o": [[0, 0], [0, -20.376], [0, 0], [19.678, 0], [0, 0], [0, 0]], "v": [[-54.824, 55.653], [-54.824, -18.759], [-19.194, -55.654], [19.193, -55.654], [54.824, -18.759], [54.824, 55.653]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8157, 0.0588]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [128.256, 195.683]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 10}, {"ty": 4, "nm": "R_Arm Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "895", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [169.348, 173.7, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [169.348, 173.7, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-30], "t": 20.689}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 31.034}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 45.517}, {"s": [-60], "t": 66.206}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[8.271, -8.271], [8.271, 8.271], [-8.271, 8.271], [-8.271, -8.271]], "o": [[-8.271, 8.271], [-8.271, -8.271], [8.271, -8.271], [8.271, 8.271]], "v": [[28.686, 28.686], [-28.686, 1.267], [-28.686, -28.686], [1.267, -28.686]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [187.971, 192.417]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11, "parent": 10}, {"ty": 4, "nm": "L_Arm Outlines", "sr": 1, "st": 0, "op": 1800, "ip": 0, "ln": "894", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [85.02, 173.7, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [85.02, 173.7, 0]}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 10.345}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [33], "t": 20.689}, {"s": [0], "t": 31.034}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "Group 1", "it": [{"ty": "sh", "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-8.271, -8.271], [-8.271, 8.271], [8.271, 8.271], [8.271, -8.271]], "o": [[8.271, 8.271], [8.271, -8.271], [-8.271, -8.271], [-8.271, 8.271]], "v": [[-28.686, 28.686], [28.686, 1.267], [28.686, -28.686], [-1.267, -28.686]]}}}, {"ty": "st", "nm": "Stroke 1", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 6.4}, "c": {"a": 0, "k": [0.3098, 0.1255, 0.051]}}, {"ty": "fl", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0.8784, 0.7451]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [68.539, 192.417]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 12, "parent": 10}]}]}