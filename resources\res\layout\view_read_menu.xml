<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/fiction_reader_menu"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/vw_menu_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <include
        android:id="@+id/reader_info"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/reader_toolbar"
        layout="@layout/fiction_reader_info"/>
    <include
        android:id="@+id/reader_toolbar"
        app:layout_constraintTop_toTopOf="parent"
        layout="@layout/fiction_reader_toolbar"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/bottom_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">
        <include
            android:id="@+id/reader_seek_bar"
            layout="@layout/fiction_reader_seeker"/>
        <include
            android:id="@+id/reader_text_style_menu"
            android:visibility="gone"
            layout="@layout/fiction_reader_text_style"/>
        <include
            android:id="@+id/reader_menu"
            layout="@layout/fiction_reader_menu"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
