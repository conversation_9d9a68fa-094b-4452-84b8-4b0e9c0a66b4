<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/reader_function_item"
    android:padding="10dp"
    android:clickable="true"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/reader_function_image"
        android:layout_width="25dp"
        android:layout_height="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/white"
        android:gravity="center"
        android:id="@+id/reader_function_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/reader_function_image"
        app:layout_constraintStart_toStartOf="@+id/reader_function_image"
        app:layout_constraintTop_toBottomOf="@+id/reader_function_image"/>
</androidx.constraintlayout.widget.ConstraintLayout>
