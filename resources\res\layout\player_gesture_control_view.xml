<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:layout_gravity="top|center_horizontal"
        android:orientation="horizontal"
        android:id="@+id/top_container"
        android:background="@drawable/bg_player_gesture_control"
        android:visibility="gone"
        android:layout_width="165dp"
        android:layout_height="28dp"
        android:layout_marginTop="15dp">
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="25dp"
            android:layout_height="25dp"/>
        <ProgressBar
            android:id="@+id/top_percent"
            android:layout_width="100dp"
            android:layout_height="3dp"
            android:max="100"
            android:progressDrawable="@drawable/player_layer_progress_bar"
            android:layout_marginStart="10dp"
            style="?android:attr/progressBarStyleHorizontal"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/center_container"
        android:background="@color/black_70_trans"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="18sp"
            android:textColor="@color/white"
            android:id="@+id/tv_percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <ProgressBar
            android:id="@+id/center_percent"
            android:layout_width="180dp"
            android:layout_height="3dp"
            android:layout_marginTop="15dp"
            android:max="100"
            android:progressDrawable="@drawable/player_layer_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"/>
    </LinearLayout>
</FrameLayout>
