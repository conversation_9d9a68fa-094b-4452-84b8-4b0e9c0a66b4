<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:layout_gravity="start|center_vertical"
        android:id="@+id/lock"
        android:background="@drawable/dkplayer_shape_back_bg"
        android:padding="@dimen/dkplayer_default_spacing"
        android:visibility="gone"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_margin="24dp"
        android:src="@drawable/dkplayer_selector_lock_button"/>
    <LinearLayout
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:id="@+id/loading"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/loading_image"
            android:layout_width="@dimen/dkplayer_play_btn_size"
            android:layout_height="@dimen/dkplayer_play_btn_size"/>
        <TextView
            android:textSize="13sp"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/video_play_prepare"/>
    </LinearLayout>
</FrameLayout>
