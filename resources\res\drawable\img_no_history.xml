<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="136dp"
    android:width="136dp"
    android:viewportWidth="136"
    android:viewportHeight="136">
    <group>
        <clip-path android:pathData="M0,0h136v136h-136z"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M119.96,110.99C127.26,98.36 122.93,82.2 110.29,74.9C97.65,67.6 81.49,71.93 74.2,84.57C66.9,97.21 71.23,113.37 83.87,120.67C96.51,127.96 112.67,123.63 119.96,110.99Z"/>
        <path
            android:fillColor="#ffeda4"
            android:pathData="M126.96,77.23C135.62,62.23 130.48,43.04 115.48,34.38C100.48,25.72 81.29,30.86 72.63,45.86C63.97,60.86 69.11,80.05 84.11,88.71C99.11,97.37 118.3,92.23 126.96,77.23Z"/>
        <path
            android:fillColor="#ffe57a"
            android:pathData="M86.17,110.31C97.47,90.75 90.77,65.74 71.21,54.45C51.65,43.15 26.64,49.86 15.35,69.41C4.05,88.97 10.76,113.98 30.31,125.27C49.87,136.57 74.88,129.87 86.17,110.31Z"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M31.4,55.68C31.4,72.71 28.91,86.52 68.71,86.52C108.51,86.52 106.03,72.71 106.03,55.68C106.03,38.66 89.32,24.85 68.71,24.85C48.1,24.85 31.4,38.66 31.4,55.68Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.4"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M62.37,68.17L56.88,76.02"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M70.08,68.17L64.6,76.02"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M77.79,68.17L72.31,76.02"
            android:strokeColor="#ffba71"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#ffd00f"
            android:pathData="M108.05,20.2C101.51,13.66 90.91,13.67 84.37,20.21C77.82,26.75 82.17,33.01 88.71,39.55C95.24,46.09 101.5,50.43 108.04,43.89C114.59,37.34 114.59,26.74 108.05,20.2ZM50.69,20.21C44.15,13.67 33.54,13.66 27,20.2C20.46,26.74 20.47,37.34 27.01,43.89C33.55,50.43 39.81,46.09 46.35,39.55C52.89,33.01 57.23,26.75 50.69,20.21Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M108.05,20.2L109.25,19L109.25,19L108.05,20.2ZM84.37,20.21L83.16,19.01L83.16,19.01L84.37,20.21ZM88.71,39.55L87.5,40.75L87.5,40.75L88.71,39.55ZM108.04,43.89L109.25,45.09L109.25,45.09L108.04,43.89ZM50.69,20.21L51.89,19.01L51.89,19.01L50.69,20.21ZM27,20.2L25.8,19L25.8,19L27,20.2ZM27.01,43.89L25.81,45.09L25.81,45.09L27.01,43.89ZM46.35,39.55L47.55,40.75L47.55,40.75L46.35,39.55ZM108.05,20.2L106.85,21.4C100.97,15.53 91.45,15.53 85.57,21.41L84.37,20.21L83.16,19.01C90.37,11.8 102.05,11.8 109.25,19L108.05,20.2ZM84.37,20.21L85.57,21.41C84.1,22.88 83.33,24.24 83.01,25.49C82.7,26.73 82.79,27.99 83.24,29.33C84.19,32.12 86.64,35.07 89.91,38.34L88.71,39.55L87.5,40.75C84.24,37.48 81.25,34.04 80.02,30.42C79.39,28.57 79.21,26.63 79.72,24.65C80.22,22.68 81.37,20.8 83.16,19.01L84.37,20.21ZM88.71,39.55L89.91,38.34C93.18,41.62 96.13,44.07 98.92,45.01C100.26,45.47 101.52,45.56 102.76,45.24C104.01,44.92 105.37,44.16 106.84,42.68L108.04,43.89L109.25,45.09C107.45,46.89 105.57,48.03 103.6,48.53C101.62,49.04 99.68,48.86 97.83,48.23C94.22,47.01 90.77,44.02 87.5,40.75L88.71,39.55ZM108.04,43.89L106.84,42.68C112.72,36.81 112.72,27.28 106.85,21.4L108.05,20.2L109.25,19C116.46,26.2 116.45,37.88 109.25,45.09L108.04,43.89ZM50.69,20.21L49.48,21.41C43.61,15.53 34.08,15.53 28.2,21.4L27,20.2L25.8,19C33,11.8 44.68,11.8 51.89,19.01L50.69,20.21ZM27,20.2L28.2,21.4C22.33,27.28 22.33,36.81 28.21,42.68L27.01,43.89L25.81,45.09C18.6,37.88 18.6,26.2 25.8,19L27,20.2ZM27.01,43.89L28.21,42.68C29.68,44.16 31.04,44.92 32.29,45.24C33.53,45.56 34.79,45.47 36.13,45.01C38.92,44.07 41.87,41.62 45.14,38.34L46.35,39.55L47.55,40.75C44.28,44.02 40.84,47.01 37.22,48.23C35.37,48.86 33.43,49.04 31.45,48.53C29.48,48.03 27.6,46.89 25.81,45.09L27.01,43.89ZM46.35,39.55L45.14,38.34C48.42,35.07 50.87,32.12 51.81,29.33C52.27,27.99 52.36,26.73 52.04,25.49C51.72,24.24 50.96,22.88 49.48,21.41L50.69,20.21L51.89,19.01C53.69,20.8 54.83,22.68 55.33,24.65C55.84,26.63 55.66,28.57 55.03,30.42C53.81,34.04 50.82,37.48 47.55,40.75L46.35,39.55Z"/>
        <path
            android:fillColor="#954a2c"
            android:pathData="M68.65,21.33C62.12,18.88 50.69,16.7 42.73,26.3C33.92,36.91 23.75,53.09 25.4,69.04C27.26,87.03 48.68,84.8 48.27,84.53C39.79,79.32 40.93,68.97 45.86,62.39C48.64,58.68 60.79,48.88 68.65,39.81C76.5,48.88 88.65,58.68 91.43,62.39C96.36,68.97 97.5,79.32 89.02,84.53C88.6,84.79 110.03,87.03 111.89,69.04C113.54,53.09 103.37,36.91 94.56,26.3C86.6,16.7 75.17,18.88 68.65,21.33Z"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M68.65,21.33L69.24,22.92L68.65,23.14L68.05,22.92L68.65,21.33ZM42.73,26.3L41.42,25.21L41.42,25.21L42.73,26.3ZM25.4,69.04L23.71,69.21L23.71,69.21L25.4,69.04ZM48.27,84.53L49.16,83.09L49.17,83.09L49.18,83.1L48.27,84.53ZM45.86,62.39L44.5,61.37L44.5,61.37L45.86,62.39ZM68.65,39.81L67.36,38.7L68.65,37.22L69.93,38.7L68.65,39.81ZM91.43,62.39L92.79,61.37L92.79,61.37L91.43,62.39ZM89.02,84.53L89.92,85.98L89.92,85.98L89.02,84.53ZM111.89,69.04L113.58,69.21L113.58,69.22L111.89,69.04ZM94.56,26.3L95.87,25.21L95.87,25.21L94.56,26.3ZM68.65,21.33L68.05,22.92C61.66,20.52 51.25,18.69 44.04,27.38L42.73,26.3L41.42,25.21C50.13,14.72 62.58,17.23 69.24,19.73L68.65,21.33ZM42.73,26.3L44.04,27.38C39.69,32.62 35.04,39.19 31.68,46.39C28.32,53.59 26.31,61.3 27.09,68.86L25.4,69.04L23.71,69.21C22.84,60.83 25.09,52.48 28.6,44.95C32.12,37.41 36.96,30.59 41.42,25.21L42.73,26.3ZM25.4,69.04L27.09,68.86C27.52,73 29.05,75.87 31.03,77.9C33.04,79.95 35.59,81.23 38.17,82C40.75,82.77 43.29,83.02 45.16,83.05C46.09,83.07 46.84,83.03 47.32,82.99C47.57,82.97 47.72,82.94 47.77,82.93C47.83,82.92 47.74,82.93 47.61,82.98C47.58,83 47.5,83.03 47.41,83.08C47.34,83.13 47.14,83.24 46.96,83.47C46.76,83.71 46.45,84.25 46.62,84.95C46.78,85.57 47.2,85.87 47.35,85.97L48.27,84.53L49.18,83.1C49.34,83.2 49.77,83.51 49.92,84.13C50.1,84.84 49.79,85.38 49.58,85.63C49.39,85.86 49.19,85.98 49.11,86.03C49.01,86.08 48.92,86.12 48.87,86.14C48.69,86.22 48.51,86.25 48.42,86.27C48.21,86.31 47.94,86.35 47.64,86.37C47.02,86.43 46.14,86.47 45.1,86.45C43.01,86.42 40.15,86.14 37.2,85.26C34.24,84.37 31.12,82.85 28.6,80.27C26.06,77.67 24.21,74.07 23.71,69.21L25.4,69.04ZM48.27,84.53L47.38,85.98C42.61,83.05 40.52,78.62 40.34,74.04C40.17,69.53 41.84,64.92 44.5,61.37L45.86,62.39L47.22,63.41C44.96,66.43 43.6,70.29 43.74,73.91C43.88,77.46 45.45,80.8 49.16,83.09L48.27,84.53ZM45.86,62.39L44.5,61.37C45.29,60.31 46.67,58.92 48.31,57.35C49.98,55.75 52.02,53.88 54.19,51.87C58.56,47.82 63.5,43.16 67.36,38.7L68.65,39.81L69.93,40.93C65.93,45.54 60.87,50.32 56.5,54.36C54.31,56.39 52.3,58.23 50.66,59.8C49,61.39 47.82,62.61 47.22,63.41L45.86,62.39ZM68.65,39.81L69.93,38.7C73.79,43.16 78.73,47.82 83.1,51.87C85.27,53.88 87.31,55.75 88.98,57.35C90.62,58.92 92,60.31 92.79,61.37L91.43,62.39L90.07,63.41C89.47,62.61 88.29,61.39 86.63,59.8C84.99,58.23 82.98,56.39 80.79,54.36C76.43,50.32 71.36,45.54 67.36,40.93L68.65,39.81ZM91.43,62.39L92.79,61.37C95.45,64.92 97.13,69.53 96.95,74.04C96.77,78.62 94.68,83.05 89.92,85.98L89.02,84.53L88.13,83.09C91.84,80.8 93.41,77.46 93.55,73.91C93.69,70.29 92.33,66.43 90.07,63.41L91.43,62.39ZM89.02,84.53L89.92,85.98C90.06,85.89 90.48,85.61 90.65,85.02C90.85,84.32 90.57,83.76 90.36,83.5C90.17,83.26 89.97,83.13 89.89,83.09C89.8,83.04 89.72,83 89.68,82.99C89.55,82.93 89.46,82.92 89.52,82.93C89.57,82.94 89.72,82.97 89.97,82.99C90.45,83.03 91.19,83.07 92.13,83.05C94,83.02 96.53,82.78 99.11,82C101.69,81.23 104.25,79.95 106.26,77.9C108.24,75.87 109.77,73 110.2,68.86L111.89,69.04L113.58,69.22C113.08,74.08 111.23,77.67 108.69,80.28C106.17,82.86 103.04,84.38 100.09,85.26C97.13,86.14 94.27,86.42 92.19,86.45C91.14,86.47 90.27,86.43 89.65,86.37C89.35,86.35 89.08,86.31 88.86,86.27C88.78,86.25 88.6,86.22 88.41,86.14C88.36,86.12 88.27,86.08 88.17,86.02C88.09,85.97 87.88,85.84 87.69,85.6C87.47,85.33 87.18,84.77 87.39,84.07C87.56,83.47 87.98,83.18 88.13,83.09L89.02,84.53ZM111.89,69.04L110.2,68.86C110.98,61.3 108.97,53.59 105.61,46.39C102.25,39.19 97.6,32.62 93.25,27.38L94.56,26.3L95.87,25.21C100.33,30.59 105.17,37.41 108.69,44.95C112.2,52.48 114.45,60.83 113.58,69.21L111.89,69.04ZM94.56,26.3L93.25,27.38C86.04,18.69 75.63,20.52 69.24,22.92L68.65,21.33L68.05,19.73C74.71,17.23 87.17,14.72 95.87,25.21L94.56,26.3Z"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M40.28,66.3L37.4,69.7L45.05,66.3L42.17,69.7"
            android:strokeColor="#ff8400"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M92.13,66.3L89.25,69.7L96.9,66.3L94.02,69.7"
            android:strokeColor="#ff8400"
            android:strokeWidth="3.4"
            android:strokeLineCap="round"
            android:strokeLineJoin="round"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M102,117.3V86.7C102,82.01 98.19,78.2 93.5,78.2H45.9C41.21,78.2 37.4,82.01 37.4,86.7V117.3C37.4,121.99 41.21,125.8 45.9,125.8H93.5C98.19,125.8 102,121.99 102,117.3Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.5934"/>
        <path
            android:fillColor="#fffae4"
            android:pathData="M95.2,112.2H40.8C37.04,112.2 34,115.24 34,119C34,122.75 37.04,125.8 40.8,125.8H95.2C98.96,125.8 102,122.75 102,119C102,115.24 98.96,112.2 95.2,112.2Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.5934"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M102,120.7V117.3C102,114.48 99.72,112.2 96.9,112.2C94.08,112.2 91.8,114.48 91.8,117.3V120.7C91.8,123.52 94.08,125.8 96.9,125.8C99.72,125.8 102,123.52 102,120.7Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.5934"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M37.51,69.7C44.1,69.7 49.51,74.92 49.51,81.44C49.51,87.97 44.1,93.18 37.51,93.18C30.92,93.18 25.5,87.96 25.5,81.44C25.5,74.91 30.92,69.7 37.51,69.7Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.3234"/>
        <path
            android:fillColor="#ffe0be"
            android:pathData="M102.11,68C108.7,68 114.12,73.22 114.12,79.74C114.12,86.27 108.7,91.48 102.11,91.48C95.52,91.48 90.1,86.26 90.1,79.74C90.1,73.21 95.52,68 102.11,68Z"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.3234"/>
        <path
            android:fillColor="#00000000"
            android:pathData="M64.6,91.82C64.6,89.3 66.68,87.26 69.22,87.33C71.56,87.39 73.52,89.35 73.58,91.69C73.64,93.85 72.18,95.67 70.19,96.17C69.55,96.33 69.09,96.87 69.09,97.52V100.12"
            android:strokeColor="#4f200d"
            android:strokeWidth="3.5934"
            android:strokeLineCap="round"/>
        <path
            android:fillColor="#4f200d"
            android:pathData="M69.09,108.76C70.65,108.76 71.92,107.49 71.92,105.93C71.92,104.37 70.65,103.11 69.09,103.11C67.53,103.11 66.27,104.37 66.27,105.93C66.27,107.49 67.53,108.76 69.09,108.76Z"/>
    </group>
</vector>
