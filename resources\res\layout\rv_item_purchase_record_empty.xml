<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="120:109"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:background="@color/white_726"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/grey_459"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/purchase_history_empty"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
