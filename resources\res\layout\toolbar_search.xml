<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:background="@color/yellow_496"
        android:layout_width="match_parent"
        android:layout_height="@dimen/actionbar_size"
        app:contentInsetStart="0dp"
        app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <EditText
                android:textSize="13sp"
                android:textColorHint="@color/grey_459"
                android:id="@+id/searchEditor"
                android:background="@drawable/bg_search_editor"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:drawablePadding="10dp"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:drawableStart="@drawable/ic_search_yellow"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:layout_marginStart="15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btnSearchCancel"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <Button
                android:id="@+id/btn_clear_search_word"
                android:background="@drawable/ic_search_cancel"
                android:visibility="invisible"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="10dp"
                app:layout_constraintBottom_toBottomOf="@+id/searchEditor"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="@+id/searchEditor"
                app:layout_constraintTop_toTopOf="@+id/searchEditor"/>
            <TextView
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:gravity="center"
                android:id="@+id/btnSearchCancel"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/searchEditor"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>
</com.google.android.material.appbar.AppBarLayout>
