<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/text_dir_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.textview.MaterialTextView
        android:textSize="16sp"
        android:textColor="@color/brown_124"
        android:gravity="center"
        android:id="@+id/item_title"
        android:background="@drawable/bg_comic_dir_item"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="82:35"/>
    <ImageView
        android:id="@+id/item_lock"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_lock"
        android:layout_marginEnd="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/more_btn"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/ic_more_brown"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/item_vip_tag"
        android:padding="1dp"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/ic_vip_tag"
        app:layout_constraintBottom_toBottomOf="@+id/item_title"
        app:layout_constraintStart_toStartOf="@+id/item_title"
        app:layout_constraintTop_toTopOf="@+id/item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
