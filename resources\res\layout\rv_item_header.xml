<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/brown_124"
        android:id="@+id/header_title"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/section_recommend"
        android:drawablePadding="10dp"
        app:drawableStartCompat="@drawable/ic_section_tag"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
