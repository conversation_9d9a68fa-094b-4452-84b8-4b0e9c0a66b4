<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/loading_bg"
        android:background="@color/black_55_trans"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.25"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Loading.Background"/>
    <ImageView
        android:id="@+id/loading_gif"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/msg"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/loading_bg"
        app:layout_constraintWidth_percent="0.15"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/white"
        android:gravity="center"
        android:id="@+id/msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/load_data_hint"
        android:drawablePadding="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/loading_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/loading_gif"/>
</androidx.constraintlayout.widget.ConstraintLayout>
