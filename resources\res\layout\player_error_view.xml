<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black_75_trans"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="13sp"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dkplayer_error_message"/>
        <com.google.android.material.button.MaterialButton
            android:textColor="@color/white"
            android:id="@+id/status_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:minHeight="0dp"
            android:text="@string/dkplayer_retry"
            android:backgroundTint="@color/yellow_497"
            app:cornerRadius="20dp"
            style="@style/Widget.App.Button"/>
    </LinearLayout>
</FrameLayout>
