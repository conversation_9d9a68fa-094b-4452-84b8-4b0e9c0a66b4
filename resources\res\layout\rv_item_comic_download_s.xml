<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/text_dir_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/bg_item"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/bg_comic_down_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="82:35"/>
    <TextView
        android:textSize="16sp"
        android:textColor="@color/brown_124"
        android:id="@+id/item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_item"
        app:layout_constraintEnd_toEndOf="@+id/bg_item"
        app:layout_constraintTop_toTopOf="@+id/bg_item"/>
    <ImageView
        android:id="@+id/icon_download_state"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_item"
        app:layout_constraintStart_toStartOf="@+id/bg_item"
        app:layout_constraintTop_toTopOf="@+id/bg_item"/>
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/icon_download_ing"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_item"
        app:layout_constraintStart_toStartOf="@+id/bg_item"
        app:layout_constraintTop_toTopOf="@+id/bg_item"
        app:lottie_autoPlay="false"
        app:lottie_fileName="save_loading2.json"
        app:lottie_loop="false"/>
    <CheckBox
        android:id="@+id/item_check"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:button="@drawable/selector_rv_item_check_s"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_item"
        app:layout_constraintStart_toStartOf="@+id/bg_item"
        app:layout_constraintTop_toTopOf="@+id/bg_item"/>
</androidx.constraintlayout.widget.ConstraintLayout>
