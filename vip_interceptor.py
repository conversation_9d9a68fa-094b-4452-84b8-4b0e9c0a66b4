from mitmproxy import http
import json

def response(flow: http.HTTPFlow) -> None:
    # 拦截用户信息API响应
    if "v2/user/info" in flow.request.pretty_url:
        print(f"拦截到用户信息请求: {flow.request.pretty_url}")
        try:
            # 解析响应JSON
            data = json.loads(flow.response.text)
            print(f"原始响应: {data}")
            
            # 修改用户等级
            if "success" in data:
                data["success"]["level"] = 3  # 设置为黄金VIP
                data["success"]["level_expiry"] = None  # 永不过期
                print(f"修改后响应: {data}")
                
            # 替换响应内容
            flow.response.text = json.dumps(data, ensure_ascii=False)
            
        except Exception as e:
            print(f"处理用户信息时出错: {e}")
    
    # 拦截VIP功能检查
    elif any(keyword in flow.request.pretty_url for keyword in ["novipMode", "novipDailog"]):
        print(f"拦截到VIP功能检查: {flow.request.pretty_url}")
        try:
            data = json.loads(flow.response.text)
            
            # 设置所有VIP功能为可用
            if "success" in data:
                for key, value in data["success"].items():
                    if isinstance(value, dict) and "status" in value:
                        value["status"] = True
                        
            flow.response.text = json.dumps(data, ensure_ascii=False)
            print("已修改VIP功能状态为可用")
            
        except Exception as e:
            print(f"处理VIP功能时出错: {e}")