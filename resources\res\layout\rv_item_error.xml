<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="14sp"
        android:textColor="@color/black_153"
        android:gravity="center"
        android:id="@+id/msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="10dp"
        app:drawableTopCompat="@drawable/img_error"
        app:layout_constraintBottom_toTopOf="@+id/btn_retry"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"/>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_retry"
        android:padding="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:minWidth="100dp"
        android:minHeight="38dp"
        android:text="@string/prompt_action_retry"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/msg"
        style="@style/Widget.Normal.Corner.Border.Button"/>
</androidx.constraintlayout.widget.ConstraintLayout>
