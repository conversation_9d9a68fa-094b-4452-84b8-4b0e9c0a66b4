<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/white"
        android:id="@+id/share"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:minWidth="0dp"
        android:text="@string/share_btn"
        android:layout_marginStart="15dp"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_share"
        app:iconSize="25dp"
        app:iconTint="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/white"
        app:strokeWidth="1dp"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/white"
        android:id="@+id/report"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="0dp"
        android:text="@string/report_btn"
        android:layout_marginStart="10dp"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_report_brown"
        app:iconSize="25dp"
        app:iconTint="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/share"
        app:layout_constraintStart_toEndOf="@+id/share"
        app:layout_constraintTop_toTopOf="@+id/share"
        app:strokeColor="@color/white"
        app:strokeWidth="1dp"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="14sp"
        android:textColor="@color/white"
        android:id="@+id/rating"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="0dp"
        android:layout_marginStart="10dp"
        app:cornerRadius="50dp"
        app:icon="@drawable/ic_star"
        app:iconGravity="end"
        app:layout_constraintBottom_toBottomOf="@+id/share"
        app:layout_constraintStart_toEndOf="@+id/report"
        app:layout_constraintTop_toTopOf="@+id/share"
        app:strokeColor="@color/white"
        app:strokeWidth="1dp"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"/>
    <ToggleButton
        android:gravity="center"
        android:id="@+id/favorite_btn"
        android:background="@drawable/selector_anime_favorite_btn"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:textOn="@null"
        android:textOff="@null"
        android:minWidth="35dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="@+id/share"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/share"/>
</androidx.constraintlayout.widget.ConstraintLayout>
