<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/anime_item_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="198dp">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anime_item_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/anime_item_title"
        app:layout_constraintDimensionRatio="194:114"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:id="@+id/anime_item_info"
        android:background="@drawable/bg_item_info"
        android:paddingLeft="5dp"
        android:paddingTop="2dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toEndOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"
        style="@style/AppTheme.AnimateItemInfoTextView"/>
    <TextView
        android:id="@+id/anime_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="@+id/anime_item_cover"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_cover"
        style="@style/AppTheme.ItemTitleTextView"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_443"
        android:id="@+id/anime_item_views"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_title"/>
    <TextView
        android:textSize="12sp"
        android:textColor="@color/grey_443"
        android:id="@+id/anime_item_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anime_item_title"/>
    <ImageView
        android:id="@+id/anime_item_tag_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_label_new"
        app:layout_constraintStart_toStartOf="@+id/anime_item_cover"
        app:layout_constraintTop_toTopOf="@+id/anime_item_cover"/>
    <ImageView
        android:id="@+id/anime_item_like"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginBottom="5dp"
        android:src="@drawable/selector_btn_like"
        android:layout_marginEnd="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/anime_item_cover"
        app:layout_constraintEnd_toEndOf="@+id/anime_item_cover"/>
</androidx.constraintlayout.widget.ConstraintLayout>
