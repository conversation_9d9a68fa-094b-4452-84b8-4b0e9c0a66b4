<?xml version="1.0" encoding="utf-8"?>
<level-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery"
        android:minLevel="95"
        android:maxLevel="100"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_10"
        android:minLevel="0"
        android:maxLevel="15"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_20"
        android:minLevel="15"
        android:maxLevel="25"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_30"
        android:minLevel="25"
        android:maxLevel="35"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_40"
        android:minLevel="35"
        android:maxLevel="45"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_50"
        android:minLevel="45"
        android:maxLevel="55"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_60"
        android:minLevel="55"
        android:maxLevel="65"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_70"
        android:minLevel="65"
        android:maxLevel="75"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_80"
        android:minLevel="75"
        android:maxLevel="85"/>
    <item
        android:drawable="@drawable/dkplayer_ic_action_battery_90"
        android:minLevel="85"
        android:maxLevel="95"/>
</level-list>
