<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_focused="false"
        android:state_selected="false"
        android:state_pressed="false"
        android:drawable="@drawable/vpi__tab_unselected_holo"/>
    <item
        android:state_focused="false"
        android:state_selected="true"
        android:state_pressed="false"
        android:drawable="@drawable/vpi__tab_selected_holo"/>
    <item
        android:state_focused="true"
        android:state_selected="false"
        android:state_pressed="false"
        android:drawable="@drawable/vpi__tab_unselected_focused_holo"/>
    <item
        android:state_focused="true"
        android:state_selected="true"
        android:state_pressed="false"
        android:drawable="@drawable/vpi__tab_selected_focused_holo"/>
    <item
        android:state_focused="false"
        android:state_selected="false"
        android:state_pressed="true"
        android:drawable="@drawable/vpi__tab_unselected_pressed_holo"/>
    <item
        android:state_focused="false"
        android:state_selected="true"
        android:state_pressed="true"
        android:drawable="@drawable/vpi__tab_selected_pressed_holo"/>
    <item
        android:state_focused="true"
        android:state_selected="false"
        android:state_pressed="true"
        android:drawable="@drawable/vpi__tab_unselected_pressed_holo"/>
    <item
        android:state_focused="true"
        android:state_selected="true"
        android:state_pressed="true"
        android:drawable="@drawable/vpi__tab_selected_pressed_holo"/>
</selector>
