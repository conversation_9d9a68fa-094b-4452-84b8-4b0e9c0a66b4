<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_459"
        android:id="@+id/rating_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/rating_title"
        android:layout_marginStart="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.mimei17.ui.SvgRatingBar
        android:id="@+id/ratingbar"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:progressDrawable="@drawable/custom_rating_bar"
        android:numStars="5"
        android:stepSize="1"
        android:isIndicator="false"
        app:layout_constraintBottom_toBottomOf="@+id/rating_btn"
        app:layout_constraintStart_toStartOf="@+id/rating_title"
        app:layout_constraintTop_toTopOf="@+id/rating_btn"
        app:spacing="10dp"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="15sp"
        android:textStyle="bold"
        android:textColor="@color/grey_612"
        android:id="@+id/rating_btn"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:text="@string/rating_send"
        android:backgroundTint="@color/white_726"
        app:cornerRadius="19dp"
        app:layout_constraintDimensionRatio="50:19"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ratingbar"
        app:layout_constraintTop_toBottomOf="@+id/rating_title"
        app:layout_constraintWidth_percent="0.27"
        style="@style/Widget.App.Button"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_459"
        android:id="@+id/msg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        app:layout_constraintStart_toStartOf="@+id/rating_title"
        app:layout_constraintTop_toBottomOf="@+id/ratingbar"/>
</androidx.constraintlayout.widget.ConstraintLayout>
