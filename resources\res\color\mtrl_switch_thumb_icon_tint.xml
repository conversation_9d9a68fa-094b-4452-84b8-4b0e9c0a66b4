<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="false"
        android:state_checked="false"
        android:color="?attr/colorSurfaceContainerHighest"
        android:alpha="@dimen/m3_comp_switch_disabled_unselected_icon_opacity"/>
    <item
        android:state_enabled="false"
        android:state_checked="true"
        android:color="?attr/colorOnSurface"
        android:alpha="@dimen/m3_comp_switch_disabled_selected_icon_opacity"/>
    <item
        android:state_checked="true"
        android:color="?attr/colorOnPrimaryContainer"/>
    <item
        android:state_checked="false"
        android:color="?attr/colorSurfaceContainerHighest"
        android:state_hovered="true"/>
    <item
        android:state_focused="true"
        android:state_checked="false"
        android:color="?attr/colorSurfaceContainerHighest"/>
    <item
        android:state_checked="false"
        android:state_pressed="true"
        android:color="?attr/colorSurfaceContainerHighest"/>
    <item
        android:state_checked="false"
        android:color="?attr/colorSurfaceContainerHighest"/>
</selector>
