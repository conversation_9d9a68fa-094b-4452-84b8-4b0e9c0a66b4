<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/bg_selected"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/bg_avatar_selected"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/avatar"
        android:padding="3dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="@+id/bg_selected"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/avatar_mask"
        android:background="@color/black_45_trans"
        android:padding="3dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="@+id/bg_selected"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Circle"/>
    <ImageView
        android:id="@+id/selected_tag"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_selected"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth="true"
        app:layout_constraintWidth_max="22dp"/>
    <ImageView
        android:id="@+id/vip_lock_tag"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/ic_vip_lock"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth="true"
        app:layout_constraintWidth_max="30dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>
