<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.Guideline
        android:orientation="vertical"
        android:id="@+id/guideline_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.14"/>
    <ImageView
        android:id="@+id/msg_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@+id/guideline_start"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.06"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/brown_389"
        android:id="@+id/msg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/msg_icon"
        app:layout_constraintStart_toEndOf="@+id/msg_icon"
        app:layout_constraintTop_toTopOf="@+id/msg_icon"/>
    <TextView
        android:textSize="13sp"
        android:textColor="@color/black_153"
        android:id="@+id/msg_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:lineSpacingExtra="6dp"
        app:layout_constraintEnd_toStartOf="@+id/favorite_btn"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@+id/msg_title"
        app:layout_constraintTop_toBottomOf="@+id/msg_title"
        app:layout_constraintWidth_percent="0.58"/>
    <TextView
        android:textSize="11sp"
        android:textColor="@color/grey_561"
        android:id="@+id/msg_datetime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        app:layout_constraintStart_toStartOf="@+id/msg_title"
        app:layout_constraintTop_toBottomOf="@+id/msg_content"/>
    <View
        android:id="@+id/msg_divider"
        android:background="@color/white_726"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:layout_marginStart="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/msg_datetime"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:gravity="end"
        android:orientation="vertical"
        android:id="@+id/favorite_btn"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:minWidth="50dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/msg_content"
        app:layout_constraintTop_toTopOf="@+id/msg_icon">
        <ToggleButton
            android:textColor="@color/grey_459"
            android:gravity="center"
            android:id="@+id/favorite_icon"
            android:background="@null"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/selector_favorite_btn"
            android:textOn="@null"
            android:textOff="@null"
            android:minWidth="0dp"
            android:minHeight="0dp"
            app:layout_constraintEnd_toEndOf="@+id/favorite_count"
            app:layout_constraintStart_toStartOf="@+id/favorite_count"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="11sp"
            android:textColor="@color/grey_459"
            android:gravity="center"
            android:id="@+id/favorite_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/favorite_icon"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
