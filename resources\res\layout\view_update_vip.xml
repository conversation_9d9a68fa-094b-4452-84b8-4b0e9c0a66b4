<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/vip_view"
    android:background="@color/black_60_trans"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/bg_dialog"
        android:background="@color/white"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="330:357"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.85"
        app:shapeAppearance="@style/ShapeAppearanceOverlay.Dialog"/>
    <ImageView
        android:id="@+id/bg_dialog_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/bg_mimei_dialog"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="360:317"
        app:layout_constraintEnd_toEndOf="@+id/bg_dialog"
        app:layout_constraintStart_toStartOf="@+id/bg_dialog"
        app:layout_constraintTop_toTopOf="@+id/bg_dialog"/>
    <ImageView
        android:id="@+id/dialog_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="25dp"
        android:src="@drawable/img_vip"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="@+id/bg_dialog"
        app:layout_constraintStart_toStartOf="@+id/bg_dialog"
        app:layout_constraintTop_toTopOf="@+id/bg_dialog"
        app:layout_constraintWidth_percent="0.33"/>
    <TextView
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/colorPrimary"
        android:id="@+id/dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintBottom_toTopOf="@+id/dialog_message"
        app:layout_constraintEnd_toEndOf="@+id/bg_dialog"
        app:layout_constraintStart_toStartOf="@+id/bg_dialog"
        app:layout_constraintTop_toBottomOf="@+id/dialog_icon"/>
    <TextView
        android:textSize="14sp"
        android:textColor="@color/grey_480"
        android:id="@+id/dialog_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="50dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="50dp"
        app:layout_constraintBottom_toTopOf="@+id/dialog_button_group"
        app:layout_constraintEnd_toEndOf="@+id/bg_dialog"
        app:layout_constraintStart_toStartOf="@+id/bg_dialog"
        app:layout_constraintTop_toBottomOf="@+id/dialog_title"/>
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/dialog_button_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/bg_dialog"
        app:layout_constraintEnd_toEndOf="@+id/bg_dialog"
        app:layout_constraintStart_toStartOf="@+id/bg_dialog"
        app:layout_constraintTop_toBottomOf="@+id/dialog_message">
        <com.google.android.material.button.MaterialButton
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/brown_202"
            android:id="@+id/dialog_left_btn"
            android:visibility="visible"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            app:rippleColor="?attr/colorControlHighlight"
            style="@style/Widget.MaterialComponents.Button.TextButton"/>
        <com.google.android.material.button.MaterialButton
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:id="@+id/dialog_right_btn"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1.5"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:backgroundTint="@color/red_433"
            app:cornerRadius="5dp"
            app:rippleColor="?attr/colorControlHighlight"
            style="@style/Widget.MaterialComponents.Button"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
