<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/item_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@color/black"
        android:alpha="0.2"
        app:layout_constraintBottom_toBottomOf="@+id/item_cover"
        app:layout_constraintTop_toTopOf="@+id/item_cover"
        app:shapeAppearanceOverlay="@style/Rounded.Image"/>
    <TextView
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:gravity="center"
        android:id="@+id/item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
