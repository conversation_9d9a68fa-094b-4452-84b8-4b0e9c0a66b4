<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/reader_gesture_hint"
    android:background="@color/black_75_trans"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/arrow_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:src="@drawable/ic_reader_hint_arrow_left"
        android:rotation="90"
        app:layout_constraintBottom_toTopOf="@+id/hint_img"
        app:layout_constraintEnd_toEndOf="@+id/hint_text"
        app:layout_constraintStart_toStartOf="@+id/hint_text"/>
    <TextView
        android:textSize="17sp"
        android:textColor="@color/white"
        android:id="@+id/text_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/comic_read_prev_page"
        android:layout_marginStart="9dp"
        app:layout_constraintBottom_toBottomOf="@+id/arrow_top"
        app:layout_constraintStart_toEndOf="@+id/arrow_top"
        app:layout_constraintTop_toTopOf="@+id/arrow_top"/>
    <ImageView
        android:id="@+id/hint_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_reader_hint_mimei"
        app:layout_constraintBottom_toTopOf="@+id/hint_text"
        app:layout_constraintEnd_toEndOf="@+id/hint_text"
        app:layout_constraintStart_toStartOf="@+id/hint_text"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"/>
    <TextView
        android:textSize="17sp"
        android:textColor="@color/white"
        android:id="@+id/text_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/comic_read_next_page"
        android:layout_marginStart="9dp"
        app:layout_constraintBottom_toBottomOf="@+id/arrow_down"
        app:layout_constraintStart_toEndOf="@+id/arrow_down"
        app:layout_constraintTop_toTopOf="@+id/arrow_down"/>
    <ImageView
        android:id="@+id/arrow_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:src="@drawable/ic_reader_hint_arrow_left"
        android:rotation="270"
        app:layout_constraintEnd_toEndOf="@+id/hint_text"
        app:layout_constraintStart_toStartOf="@+id/hint_text"
        app:layout_constraintTop_toBottomOf="@+id/hint_text"/>
    <TextView
        android:textSize="15sp"
        android:textColor="@color/white"
        android:id="@+id/hint_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/comic_read_gesture_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hint_img"/>
</androidx.constraintlayout.widget.ConstraintLayout>
