<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="108dp"
    android:width="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    <group
        android:scaleX="0.58"
        android:scaleY="0.58"
        android:translateX="22.68"
        android:translateY="22.68">
        <path
            android:fillColor="#f9c731"
            android:pathData="M90.1,60.2c0.1,-2.4 -0.8,-5 -2.7,-6.9c-3.6,-3.6 -9.1,-3.6 -12.6,-0.2c-1.6,1.6 -2.3,3.6 -2.4,5.6c-3.3,1.6 -5.3,4.7 -6.6,7c-2.8,1.3 -6.5,3.9 -7.6,8.1c-2.1,0 -4.2,0.9 -5.8,2.4c-3.2,3.2 -3.2,8.6 0,11.9c2.2,2.2 5.2,2.9 8,2.2c1.6,4.6 4,8.8 7.7,11.8c8,6.6 15.2,-2.3 15,-2.3c-5.1,1.7 -11.5,0.4 -13.8,-4.2c7,4 16.3,2.9 22.4,-3.2c4.5,-4.5 6.2,-10.6 5.3,-16.3c1.9,1.8 3,4.2 2.3,7.3c-0.1,0.2 9.5,-7.5 3,-15.5C99.2,64.2 94.8,61.8 90.1,60.2z"
            android:strokeColor="#ffffff"
            android:strokeWidth="4"/>
        <path
            android:fillColor="#fbd1b1"
            android:pathData="M65.48,92.76a18.7,18 135,1 0,26.45 -26.45a18.7,18 135,1 0,-26.45 26.45z"/>
        <path
            android:fillColor="#be6b55"
            android:pathData="M91.2,73.4c4.7,0.7 9.2,4.3 7.9,9.9c-0.1,0.2 9.5,-7.5 3,-15.5c-5.8,-7.1 -16.4,-9.5 -24.2,-10.2c-9,-0.9 -12.6,8.3 -13.4,10.9C62.3,76.3 86.5,72.8 91.2,73.4z"/>
        <path
            android:fillColor="#be6b55"
            android:pathData="M68.4,92.4c0.6,7.2 8.5,9.3 14.6,7.3c0.2,-0.1 -7,8.8 -14.9,2.3c-7.1,-5.8 -9.5,-16.4 -10.2,-24.2c-0.9,-9 8.5,-12.6 10.9,-13.4C79.7,60.9 67.8,85.2 68.4,92.4z"/>
        <path
            android:fillColor="#be6b55"
            android:pathData="M86.7,87.7c-0.6,0.6 -1.2,0.8 -2,0.7c0.1,0.8 -0.1,1.4 -0.7,2c-1,1 -2.7,0.9 -3.8,-0.2c-0.2,-0.2 -0.3,-0.7 0,-0.9c0.2,-0.2 0.7,-0.2 0.9,0c0.6,0.6 1.4,0.6 1.9,0.1s0.4,-1.3 -0.1,-1.9c-0.2,-0.2 -0.3,-0.7 0,-0.9c0.2,-0.2 0.7,-0.2 0.9,0c0.6,0.6 1.4,0.6 1.9,0.1c0.4,-0.4 0.4,-1.3 -0.1,-1.9c-0.2,-0.2 -0.3,-0.7 0,-0.9c0.3,-0.2 0.7,-0.2 0.9,0C87.6,85 87.7,86.8 86.7,87.7z"/>
        <path
            android:fillColor="#f9c731"
            android:pathData="M49.9,82.1a8.3,8.3 0,1 0,16.6 0a8.3,8.3 0,1 0,-16.6 0z"/>
        <path
            android:fillColor="#f9c731"
            android:pathData="M72.1,59.3a8.9,8.9 0,1 0,17.8 0a8.9,8.9 0,1 0,-17.8 0z"/>
        <path
            android:fillColor="#ff82a1"
            android:pathData="M88.91,74.92a2.8,1.9 135,1 0,3.96 -3.96a2.8,1.9 135,1 0,-3.96 3.96z"/>
        <path
            android:fillColor="#ff82a1"
            android:pathData="M67.05,95.58a2.8,1.9 135,1 0,3.96 -3.96a2.8,1.9 135,1 0,-3.96 3.96z"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M26.2,24.4l4,-4l-8.6,-8.6l2.6,-2.6l8.6,8.6l3.9,-3.9l2.6,2.6l-3.9,3.9l0.4,0.4c4,0 7.6,0.2 10.8,0.8v3.9c-2.3,-0.7 -4.9,-1.2 -7.7,-1.5l6.9,6.9l-2.6,2.6L36,26.2c1.2,3.2 2,6.3 2.3,9.1l-4,-0.8c0,-3.6 -0.8,-7.2 -2.2,-10.8L28.7,27L26.2,24.4zM27.9,8.6L30.7,7c1.8,2.6 3.2,5.1 4.2,7.4l-3,1.4C31,13.6 29.7,11.3 27.9,8.6zM21.2,15.7c2.2,0.9 4.5,2.1 7,3.7l-1.6,2.7c-2.2,-1.3 -4.5,-2.5 -6.8,-3.5L21.2,15.7zM14,20.1c3,0.6 5.5,1.2 7.5,1.9l-0.7,3.5c-2.2,-0.8 -4.7,-1.5 -7.6,-2.1L14,20.1zM46.9,31.1c1.7,-1.7 3.3,-3.3 4.7,-4.9l2,3.4L49.3,34c-0.9,0.9 -1.8,1.7 -2.7,2.6c-1.3,1.2 -2.6,2.2 -4,3c-1.3,0.7 -2.7,1 -4.1,0.9c-0.8,0 -1.2,0.1 -1.5,0.4c-0.4,0.4 -0.3,2.3 0.5,5.7L33.8,46c-0.6,-2.9 -0.7,-5 -0.4,-6.4L25,31.2l-2.2,2.2l-2.5,-2.5l4.8,-4.8l11.2,11.2c1.2,0.1 2.5,-0.1 3.7,-0.5c1.2,-0.6 2.4,-1.4 3.6,-2.4C44.7,33.3 45.8,32.2 46.9,31.1zM7.2,26.1c2.9,-0.1 5.6,0 8,0.4v3.9c-2.2,-0.5 -4.9,-0.6 -8.1,-0.5L7.2,26.1zM12,37.5l5.3,-5.3l10.9,10.9c0,-1.2 0,-2.6 0,-4l3.4,2c0.1,3.1 -0.2,6.2 -0.7,9.2l-3.5,-1.4c0.1,-0.7 -0.1,-1.4 -0.7,-1.9l-9.5,-9.5l-2.7,2.7L12,37.5z"/>
        <path
            android:fillColor="#ffffff"
            android:pathData="M13.2,77.6l9.6,9.6l-2,2l-7.7,-7.7L13,81.6l-0.8,3.6l-1.8,-1.8l0.9,-3.9L13.2,77.6zM25.2,84.7l-3.9,-11.8l-0.1,-0.1l-4.6,4.6l-1.7,-1.7l6.7,-6.7l1.7,1.7l3.9,11.9L25.2,84.7zM32.4,77.5l-7.2,-7.2l1.9,-1.9l1.3,1.3l0.1,-0.1c-0.3,-0.6 -0.4,-1.2 -0.2,-1.7c0.1,-0.6 0.4,-1.1 0.9,-1.6s1,-0.8 1.6,-0.9s1.2,0 1.7,0.3l0.1,-0.1c-0.3,-0.6 -0.3,-1.2 -0.2,-1.8c0.1,-0.6 0.5,-1.2 1,-1.7c0.7,-0.7 1.4,-1 2.3,-1c0.8,0 1.7,0.4 2.4,1.2l4.8,4.8l-2,2l-4.4,-4.4C36,64.2 35.6,64 35.2,64c-0.4,0 -0.8,0.2 -1.1,0.5c-0.4,0.4 -0.5,0.8 -0.5,1.2c0,0.4 0.2,0.8 0.6,1.2l4.4,4.4l-1.9,1.9l-4.5,-4.5c-0.4,-0.4 -0.7,-0.5 -1.1,-0.5s-0.8,0.1 -1.1,0.5c-0.2,0.2 -0.4,0.5 -0.4,0.8c-0.1,0.3 0,0.6 0,0.9c0.1,0.3 0.3,0.6 0.5,0.8l4.3,4.3L32.4,77.5zM44.2,65.7L37,58.5l2,-2l7.2,7.2L44.2,65.7zM37.1,56.6c-0.3,0.3 -0.6,0.5 -1.1,0.5c-0.4,0 -0.8,-0.1 -1,-0.4c-0.3,-0.3 -0.4,-0.6 -0.4,-1s0.2,-0.8 0.5,-1.1s0.6,-0.4 1.1,-0.5c0.4,0 0.8,0.1 1,0.4c0.3,0.3 0.4,0.6 0.4,1S37.4,56.3 37.1,56.6zM47.6,62.3l-7.2,-7.2l1.9,-1.9l1.3,1.3l0.1,-0.1c-0.3,-0.6 -0.4,-1.2 -0.2,-1.7c0.1,-0.6 0.4,-1.1 0.9,-1.6s1,-0.8 1.6,-0.9c0.6,-0.1 1.2,0 1.7,0.3l0.1,-0.1c-0.3,-0.6 -0.3,-1.2 -0.2,-1.8s0.5,-1.2 1,-1.7c0.7,-0.7 1.4,-1 2.3,-1c0.8,0 1.7,0.4 2.4,1.2l4.8,4.8l-2,2l-4.4,-4.4c-0.4,-0.4 -0.8,-0.6 -1.2,-0.6s-0.8,0.2 -1.1,0.5c-0.4,0.4 -0.5,0.8 -0.5,1.2s0.2,0.8 0.6,1.2l4.4,4.4L52,58.1l-4.5,-4.5C47,53.2 46.6,53 46.2,53c-0.4,0 -0.8,0.1 -1.1,0.5c-0.2,0.2 -0.4,0.5 -0.4,0.8c-0.1,0.3 0,0.6 0,0.9c0.1,0.3 0.3,0.6 0.5,0.8l4.3,4.3L47.6,62.3zM62.8,47.4c-0.7,0.7 -1.5,1.2 -2.4,1.5c-0.8,0.2 -1.7,0.2 -2.5,-0.1s-1.6,-0.8 -2.4,-1.5c-0.7,-0.7 -1.2,-1.5 -1.5,-2.4c-0.3,-0.8 -0.3,-1.7 -0.1,-2.5s0.7,-1.6 1.4,-2.3c0.5,-0.5 1,-0.8 1.6,-1.1s1.1,-0.4 1.8,-0.4c0.6,0 1.2,0.1 1.9,0.4c0.6,0.3 1.2,0.7 1.9,1.3l0.5,0.5l-6.1,6.1l-1.2,-1.2l4.2,-4.2c-0.3,-0.3 -0.6,-0.5 -0.9,-0.6c-0.3,-0.1 -0.7,-0.1 -1,0s-0.7,0.3 -1,0.6s-0.5,0.6 -0.6,1s-0.1,0.7 0,1.1c0.1,0.4 0.3,0.7 0.6,1l1.2,1.2c0.4,0.4 0.7,0.6 1.1,0.7c0.4,0.1 0.8,0.1 1.2,0s0.7,-0.3 1.1,-0.7c0.2,-0.2 0.4,-0.5 0.5,-0.7s0.2,-0.5 0.2,-0.8s0,-0.5 -0.2,-0.8l2,-1.7c0.3,0.5 0.5,1.1 0.6,1.7c0,0.6 -0.1,1.2 -0.3,1.9C63.8,46.2 63.4,46.8 62.8,47.4zM67,42.9l-7.2,-7.2l2,-2l7.2,7.2L67,42.9zM59.9,33.8c-0.3,0.3 -0.6,0.5 -1.1,0.5c-0.4,0 -0.8,-0.1 -1,-0.4c-0.3,-0.3 -0.4,-0.6 -0.4,-1s0.2,-0.8 0.5,-1.1c0.3,-0.3 0.6,-0.4 1.1,-0.5c0.4,0 0.8,0.1 1,0.4c0.3,0.3 0.4,0.6 0.4,1S60.2,33.5 59.9,33.8zM71.7,38.5C71.4,38.8 71,39 70.6,39s-0.8,-0.2 -1.1,-0.5c-0.3,-0.3 -0.5,-0.7 -0.5,-1.1c0,-0.4 0.2,-0.8 0.5,-1.1s0.7,-0.5 1.1,-0.5s0.8,0.1 1.1,0.4c0.2,0.2 0.3,0.4 0.4,0.7s0.1,0.5 0,0.8C72,38 71.9,38.3 71.7,38.5zM70.8,24.8l1.5,1.5l-4.4,4.4l-1.5,-1.5L70.8,24.8zM74.5,35.4l-7.7,-7.7c-0.5,-0.5 -0.9,-1.1 -1,-1.6s-0.1,-1.1 0.1,-1.6s0.5,-1 1,-1.5c0.3,-0.3 0.6,-0.6 0.9,-0.8c0.3,-0.2 0.5,-0.4 0.7,-0.4l1.1,1.9c-0.1,0.1 -0.2,0.1 -0.4,0.2c-0.1,0.1 -0.3,0.2 -0.4,0.3c-0.3,0.3 -0.4,0.6 -0.4,0.8c0,0.2 0.1,0.5 0.4,0.7l7.7,7.7L74.5,35.4zM80.5,23.3l-4.1,-4.1l2,-2l7.2,7.2l-1.9,1.9L82.3,25l0,0c0.3,0.6 0.3,1.2 0.2,1.8c-0.1,0.6 -0.4,1.2 -1,1.8c-0.5,0.5 -1,0.8 -1.6,0.9s-1.2,0.1 -1.8,-0.1c-0.6,-0.2 -1.2,-0.6 -1.8,-1.1l-4.6,-4.6l2,-2L78,26c0.4,0.4 0.9,0.6 1.3,0.7c0.5,0 0.9,-0.2 1.3,-0.5c0.2,-0.2 0.4,-0.5 0.5,-0.8s0.1,-0.7 0,-1C81,23.9 80.8,23.6 80.5,23.3zM84.8,16.8L89,21l-2,2l-7.2,-7.2l1.9,-1.9l1.3,1.3l0.1,-0.1c-0.3,-0.6 -0.3,-1.2 -0.2,-1.8c0.1,-0.6 0.5,-1.2 1,-1.7s1,-0.8 1.6,-1s1.2,-0.1 1.8,0.1c0.6,0.2 1.2,0.6 1.7,1.1l4.6,4.6l-2,2l-4.2,-4.2c-0.4,-0.4 -0.9,-0.7 -1.4,-0.7s-0.9,0.2 -1.3,0.6c-0.3,0.3 -0.4,0.6 -0.5,0.9c-0.1,0.3 -0.1,0.6 0,1C84.3,16.2 84.5,16.5 84.8,16.8z"/>
    </group>
</vector>
